{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"UnoSkiaApp/1.0": {"dependencies": {"Microsoft.Extensions.Logging.Console": "9.0.6", "SkiaSharp.Skottie": "3.119.0-preview.1.2", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "Uno.Dsp.Tasks": "1.4.0", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Core.WinUI": "6.0.12", "Uno.Extensions.Hosting.WinUI": "6.0.12", "Uno.Extensions.Http.Kiota": "6.0.12", "Uno.Extensions.Http.WinUI": "6.0.12", "Uno.Extensions.Localization.WinUI": "6.0.12", "Uno.Extensions.Logging.WinUI": "6.0.12", "Uno.Extensions.Navigation.Toolkit.WinUI": "6.0.12", "Uno.Extensions.Navigation.WinUI": "6.0.12", "Uno.Extensions.Reactive.Messaging": "6.0.12", "Uno.Extensions.Reactive.WinUI": "6.0.12", "Uno.Extensions.Serialization.Http": "6.0.12", "Uno.Extensions.Serialization.Refit": "6.0.12", "Uno.Fonts.OpenSans": "2.7.1", "Uno.Material.WinUI": "5.5.4", "Uno.Resizetizer": "1.8.1", "Uno.Sdk.Extras": "5.6.3", "Uno.Settings.DevServer": "1.3.12", "Uno.Toolkit.WinUI": "7.0.7", "Uno.Toolkit.WinUI.Material": "7.0.7", "Uno.UI.Adapter.Microsoft.Extensions.Logging": "6.0.797", "Uno.UI.HotDesign": "1.13.6", "Uno.WinUI": "6.0.797", "Uno.WinUI.DevServer": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797", "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer": "6.0.797", "Uno.WinUI.Runtime.Skia.MacOS": "6.0.797", "Uno.WinUI.Runtime.Skia.Win32": "6.0.797", "Uno.WinUI.Runtime.Skia.Wpf": "6.0.797", "Uno.WinUI.Runtime.Skia.X11": "6.0.797"}, "runtime": {"UnoSkiaApp.dll": {}}}, "CommonServiceLocator/2.0.5": {"runtime": {"lib/netcoreapp3.0/CommonServiceLocator.dll": {"assemblyVersion": "2.0.5.0", "fileVersion": "2.0.5.0"}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "HarfBuzzSharp/8.3.1.1-preview.1.2": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "8.3.1.1-preview.1.2", "HarfBuzzSharp.NativeAssets.macOS": "8.3.1.1-preview.1.2"}, "runtime": {"lib/net8.0/HarfBuzzSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.1.1"}}}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1-preview.1.2": {"runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-loongarch64/native/libHarfBuzzSharp.so": {"rid": "linux-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libHarfBuzzSharp.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-riscv64/native/libHarfBuzzSharp.so": {"rid": "linux-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libHarfBuzzSharp.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1-preview.1.2": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1-preview.1.2": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "LibVLCSharp/3.7.0": {"runtime": {"lib/net6.0/LibVLCSharp.dll": {"assemblyVersion": "3.7.0.0", "fileVersion": "3.7.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Logging.Console": "9.0.6", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.123.58008"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Console/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.EventLog": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Kiota.Abstractions/1.16.4": {"dependencies": {"Std.UriTemplate": "2.0.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Http.HttpClientLibrary/1.16.4": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Form/1.16.4": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Form.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Json/1.16.4": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Json.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Multipart/1.16.4": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Multipart.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Text/1.16.4": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Text.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Windows.CsWin32/0.3.106": {"dependencies": {"Microsoft.Windows.SDK.Win32Docs": "0.1.42-alpha", "Microsoft.Windows.SDK.Win32Metadata": "60.0.34-preview", "Microsoft.Windows.WDK.Win32Metadata": "0.11.4-experimental"}}, "Microsoft.Windows.SDK.Win32Docs/0.1.42-alpha": {}, "Microsoft.Windows.SDK.Win32Metadata/60.0.34-preview": {}, "Microsoft.Windows.WDK.Win32Metadata/0.11.4-experimental": {"dependencies": {"Microsoft.Windows.SDK.Win32Metadata": "60.0.34-preview"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Refit/7.2.22": {"runtime": {"lib/net8.0/Refit.dll": {"assemblyVersion": "7.2.0.0", "fileVersion": "7.2.22.0"}}}, "SkiaSharp/3.119.0-preview.1.2": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.macOS": "3.119.0-preview.1.2"}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"assemblyVersion": "3.119.0.0", "fileVersion": "3.119.0.0"}}}, "SkiaSharp.NativeAssets.Linux/3.119.0-preview.1.2": {"runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-loongarch64/native/libSkiaSharp.so": {"rid": "linux-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libSkiaSharp.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libSkiaSharp.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-loongarch64/native/libSkiaSharp.so": {"rid": "linux-musl-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-riscv64/native/libSkiaSharp.so": {"rid": "linux-musl-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-riscv64/native/libSkiaSharp.so": {"rid": "linux-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libSkiaSharp.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0-preview.1.2": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.WebAssembly/3.119.0-preview.1.2": {}, "SkiaSharp.NativeAssets.Win32/3.119.0-preview.1.2": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.Resources/3.119.0-preview.1.2": {"dependencies": {"SkiaSharp": "3.119.0-preview.1.2"}, "runtime": {"lib/net8.0/SkiaSharp.Resources.dll": {"assemblyVersion": "3.119.0.0", "fileVersion": "3.119.0.0"}}}, "SkiaSharp.SceneGraph/3.119.0-preview.1.2": {"dependencies": {"SkiaSharp": "3.119.0-preview.1.2"}, "runtime": {"lib/net8.0/SkiaSharp.SceneGraph.dll": {"assemblyVersion": "3.119.0.0", "fileVersion": "3.119.0.0"}}}, "SkiaSharp.Skottie/3.119.0-preview.1.2": {"dependencies": {"SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.Resources": "3.119.0-preview.1.2", "SkiaSharp.SceneGraph": "3.119.0-preview.1.2"}, "runtime": {"lib/net8.0/SkiaSharp.Skottie.dll": {"assemblyVersion": "3.119.0.0", "fileVersion": "3.119.0.0"}}}, "SkiaSharp.Views.Uno.WinUI/3.119.0-preview.1.2": {"dependencies": {"SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.WebAssembly": "3.119.0-preview.1.2", "Uno.WinUI": "6.0.797"}, "runtime": {"uno-runtime/net8.0/skia/SkiaSharp.Views.Windows.dll": {"assemblyVersion": "3.119.0.0", "fileVersion": "3.119.0.0"}}}, "Std.UriTemplate/2.0.1": {"runtime": {"lib/net5.0/Std.UriTemplate.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "System.Collections.Immutable/8.0.0": {}, "System.Diagnostics.EventLog/8.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.IO.Pipelines/8.0.0": {}, "System.Json/4.7.1": {"runtime": {"lib/netstandard2.0/System.Json.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "System.Linq.Async/4.0.0": {"runtime": {"lib/netcoreapp3.0/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Json/8.0.5": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Tmds.DBus.Protocol/0.21.2": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Tmds.DBus.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Uno.Core.Extensions/4.1.1": {"runtime": {"lib/net7.0/Uno.Core.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Core.Extensions.Collections/4.1.1": {"dependencies": {"Uno.Core.Extensions.Disposables": "4.1.1", "Uno.Core.Extensions.Equality": "4.1.1"}, "runtime": {"lib/net7.0/Uno.Core.Extensions.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Core.Extensions.Disposables/4.1.1": {"runtime": {"lib/net7.0/Uno.Core.Extensions.Disposables.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Core.Extensions.Equality/4.1.1": {"runtime": {"lib/net7.0/Uno.Core.Extensions.Equality.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Core.Extensions.Logging/4.0.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net6.0/Uno.Core.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Core.Extensions.Logging.Singleton/4.1.1": {"dependencies": {"CommonServiceLocator": "2.0.5", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net7.0/Uno.Core.Extensions.Logging.Singleton.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Diagnostics.Eventing/2.0.1": {"runtime": {"lib/net5.0/Uno.Diagnostics.Eventing.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Dsp.Tasks/1.4.0": {}, "Uno.Extensions.Authentication/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.Extensions.Storage": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Authentication.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Configuration/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Serialization": "6.0.12", "Uno.Extensions.Storage": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Configuration.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Core/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net8.0/Uno.Extensions.Core.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Core.WinUI/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Core.UI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Hosting/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Hosting.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Hosting.WinUI/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Core.WinUI": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Storage": "6.0.12", "Uno.Extensions.Storage.WinUI": "6.0.12", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Hosting.WinUI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Http/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Http.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Http.Kiota/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Kiota.Abstractions": "1.16.4", "Microsoft.Kiota.Http.HttpClientLibrary": "1.16.4", "Microsoft.Kiota.Serialization.Form": "1.16.4", "Microsoft.Kiota.Serialization.Json": "1.16.4", "Microsoft.Kiota.Serialization.Multipart": "1.16.4", "Microsoft.Kiota.Serialization.Text": "1.16.4", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Authentication": "6.0.12", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.Extensions.Serialization": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Http.Kiota.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Http.WinUI/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Http.WinUI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Localization/6.0.12": {"runtime": {"lib/net8.0/Uno.Extensions.Localization.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Localization.WinUI/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Localization": "6.0.12", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Localization.WinUI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Logging.WebAssembly.Console/1.7.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net7.0/Uno.Extensions.Logging.WebAssembly.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Extensions.Logging.WinUI/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Logging.WebAssembly.Console": "1.7.0", "Uno.UI.Adapter.Microsoft.Extensions.Logging": "6.0.797", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Logging.WinUI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Navigation/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Navigation.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Navigation.Toolkit.WinUI/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions": "4.1.1", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Core.WinUI": "6.0.12", "Uno.Extensions.Navigation.WinUI": "6.0.12", "Uno.Toolkit.WinUI": "7.0.7", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Navigation.Toolkit.UI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Navigation.WinUI/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions": "4.1.1", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Core.WinUI": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Hosting.WinUI": "6.0.12", "Uno.Extensions.Navigation": "6.0.12", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Navigation.UI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Reactive/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Linq.Async": "4.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Toolkit": "7.0.7"}, "runtime": {"lib/net8.0/Uno.Extensions.Reactive.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Reactive.Messaging/6.0.12": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Linq.Async": "4.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Reactive": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Reactive.Messaging.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Reactive.WinUI/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Async": "4.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Extensions.Reactive": "6.0.12", "Uno.Toolkit.WinUI": "7.0.7", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Reactive.UI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Serialization/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Serialization.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Serialization.Http/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.Extensions.Serialization": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Serialization.Http.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Serialization.Refit/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "Refit": "7.2.22", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Serialization": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Serialization.Refit.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Storage/6.0.12": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Serialization": "6.0.12"}, "runtime": {"lib/net8.0/Uno.Extensions.Storage.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Extensions.Storage.WinUI/6.0.12": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "9.0.6", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Serialization": "6.0.12", "Uno.Extensions.Storage": "6.0.12", "Uno.WinUI": "6.0.797", "Uno.WinUI.Graphics2DSK": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Storage.UI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Fonts.Fluent/2.6.1": {"runtime": {"lib/netstandard1.0/Uno.Fonts.Fluent.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Fonts.OpenSans/2.7.1": {"runtime": {"lib/net7.0/Uno.Fonts.OpenSans.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Fonts.Roboto/2.2.2": {"runtime": {"lib/netstandard2.0/Uno.Fonts.Roboto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Foundation/6.0.797": {"runtime": {"uno-runtime/net9.0/skia/Uno.Foundation.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Foundation.Logging/6.0.797": {"runtime": {"lib/net8.0/Uno.Foundation.Logging.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Material.WinUI/5.5.4": {"dependencies": {"Uno.Core.Extensions.Disposables": "4.1.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Fonts.Roboto": "2.2.2", "Uno.Themes.WinUI": "5.5.4", "Uno.WinUI": "6.0.797", "Uno.WinUI.Lottie": "6.0.797"}, "runtime": {"lib/net8.0/Uno.Material.WinUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Resizetizer/1.8.1": {}, "Uno.Sdk.Extras/5.6.3": {}, "Uno.Settings.DevServer/1.3.12": {}, "Uno.Themes.WinUI/5.5.4": {"dependencies": {"Uno.Core.Extensions.Disposables": "4.1.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Fonts.Roboto": "2.2.2", "Uno.WinUI": "6.0.797"}, "runtime": {"lib/net8.0/Uno.Themes.WinUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Toolkit/7.0.7": {"runtime": {"lib/netstandard2.0/Uno.Toolkit.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Toolkit.WinUI/7.0.7": {"dependencies": {"Uno.Core.Extensions.Collections": "4.1.1", "Uno.Core.Extensions.Logging": "4.0.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Toolkit": "7.0.7", "Uno.WinUI": "6.0.797"}, "runtime": {"lib/net8.0/Uno.Toolkit.WinUI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.Toolkit.WinUI.Material/7.0.7": {"dependencies": {"Uno.Material.WinUI": "5.5.4", "Uno.Toolkit.WinUI": "7.0.7", "Uno.WinUI": "6.0.797"}, "runtime": {"lib/net8.0/Uno.Toolkit.WinUI.Material.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.UI.Adapter.Microsoft.Extensions.Logging/6.0.797": {"dependencies": {"Uno.Core.Extensions.Logging": "4.0.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Foundation.Logging": "6.0.797"}, "runtime": {"lib/net9.0/Uno.UI.Adapter.Microsoft.Extensions.Logging.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.UI.HotDesign/1.13.6": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Microsoft.Extensions.Logging": "9.0.6", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Themes.WinUI": "5.5.4", "Uno.Toolkit.WinUI": "7.0.7", "Uno.WinUI": "6.0.797", "Uno.WinUI.DevServer": "6.0.797", "Uno.WinUI.DevServer.Messaging": "6.0.797"}, "runtime": {"lib/net8.0/Uno.UI.HotDesign.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Client.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.CommunityToolkit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Hierarchy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Messaging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.PropertyGrid.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Toolbox.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactivity.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Uno.UI.HotDesign.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.Wasm.WebSockets/1.1.0": {"runtime": {"lib/netstandard2.0/Uno.Wasm.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Uno.WinRT/6.0.797": {"dependencies": {"Uno.Foundation": "6.0.797"}, "runtime": {"uno-runtime/net9.0/skia/Uno.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "uno-runtime/net9.0/skia/Uno.UI.Dispatching.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI/6.0.797": {"dependencies": {"Uno.Diagnostics.Eventing": "2.0.1", "Uno.Fonts.Fluent": "2.6.1", "Uno.Foundation.Logging": "6.0.797", "Uno.WinRT": "6.0.797"}, "runtime": {"uno-runtime/net9.0/skia/Uno.UI.Composition.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "uno-runtime/net9.0/skia/Uno.UI.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.v1.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.v2.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "uno-runtime/net9.0/skia/Uno.UI.Toolkit.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "uno-runtime/net9.0/skia/Uno.Xaml.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.DevServer/6.0.797": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Uno.Wasm.WebSockets": "1.1.0", "Uno.WinUI": "6.0.797", "Uno.WinUI.DevServer.Messaging": "6.0.797"}, "runtime": {"uno-runtime/net9.0/skia/Uno.UI.RemoteControl.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.DevServer.Messaging/6.0.797": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Uno.UI.RemoteControl.Messaging.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.Graphics2DSK/6.0.797": {"dependencies": {"SkiaSharp": "3.119.0-preview.1.2", "Uno.WinUI": "6.0.797"}, "runtime": {"lib/net9.0/Uno.WinUI.Graphics2DSK.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.Lottie/6.0.797": {"dependencies": {"System.Json": "4.7.1", "Uno.WinUI": "6.0.797"}, "runtime": {"uno-runtime/net9.0/skia/Uno.UI.Lottie.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.Runtime.Skia/6.0.797": {"dependencies": {"Uno.WinUI": "6.0.797"}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer/6.0.797": {"dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "HarfBuzzSharp.NativeAssets.Linux": "8.3.1.1-preview.1.2", "Microsoft.Win32.Registry": "4.7.0", "SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.Linux": "3.119.0-preview.1.2", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.Linux.FrameBuffer.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.Runtime.Skia.MacOS/6.0.797": {"dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "HarfBuzzSharp.NativeAssets.macOS": "8.3.1.1-preview.1.2", "SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.macOS": "3.119.0-preview.1.2", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.MacOS.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}, "runtimeTargets": {"runtimes/osx/native/libUnoNativeMac.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Uno.WinUI.Runtime.Skia.Win32/6.0.797": {"dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "Microsoft.Windows.CsWin32": "0.3.106", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.Win32.Support.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}, "lib/net9.0/Uno.UI.Runtime.Skia.Win32.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.Runtime.Skia.Wpf/6.0.797": {"dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "Microsoft.Win32.SystemEvents": "9.0.0", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.Wpf.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}, "Uno.WinUI.Runtime.Skia.X11/6.0.797": {"dependencies": {"HarfBuzzSharp.NativeAssets.Linux": "8.3.1.1-preview.1.2", "HarfBuzzSharp": "8.3.1.1-preview.1.2", "LibVLCSharp": "3.7.0", "SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.Linux": "3.119.0-preview.1.2", "Tmds.DBus.Protocol": "0.21.2", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.X11.dll": {"assemblyVersion": "***************", "fileVersion": "***************"}}}}}, "libraries": {"UnoSkiaApp/1.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommonServiceLocator/2.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Md2q9f6hAuUAm6IWTybQbYvxhDAs2dqmsudVDghpg0Q+C0M2KkmiTDjeGx1PsYhnZPvp+hy1Cx99v0VPW9W6/g==", "path": "commonservicelocator/2.0.5", "hashPath": "commonservicelocator.2.0.5.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "HarfBuzzSharp/8.3.1.1-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-raAYK1w/dRFWmyiIW+KpDj5zhUdQDtbzTK7q6YP+qtHmbhrHS3re0MyQrfI8ypOCMnPiUItP/R5ellizN6pbHg==", "path": "harfbuzzsharp/8.3.1.1-preview.1.2", "hashPath": "harfbuzzsharp.8.3.1.1-preview.1.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zftAUE5axEqe/F7/s9/h/SAw6Bt+Bu1htv7Fk1NvZNfx0l/5vxnGwG/lFcfKA1pFo8sMffTtTLaRo8eVEM34Ag==", "path": "harfbuzzsharp.nativeassets.linux/8.3.1.1-preview.1.2", "hashPath": "harfbuzzsharp.nativeassets.linux.8.3.1.1-preview.1.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zOV4y9jvG5AIAiFTpjOdXqEum+yWoqRV88Eq008IbP/qxqG+UwUCDRsYIxbiCFRGpSNWG7u1nJO54pPITT+swg==", "path": "harfbuzzsharp.nativeassets.macos/8.3.1.1-preview.1.2", "hashPath": "harfbuzzsharp.nativeassets.macos.8.3.1.1-preview.1.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-8ZSNKExR0ewPWfR79eCID9z2j95kcrhLP+34mIkjfiD/SKq5CA84pP8TDJw5/oDxPorZ5i9DCKR+C9CaNESOBA==", "path": "harfbuzzsharp.nativeassets.win32/8.3.1.1-preview.1.2", "hashPath": "harfbuzzsharp.nativeassets.win32.8.3.1.1-preview.1.2.nupkg.sha512"}, "LibVLCSharp/3.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-N9NV+BamKr+1YiRktX9JroE4m9C+pEszUNW4qa3A5mychhCWBvvoDolZKWk2yxhZvHewgaXXahBcw5ivCXjKYA==", "path": "libvlcsharp/3.7.0", "hashPath": "libvlcsharp.3.7.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "path": "microsoft.extensions.configuration/9.0.6", "hashPath": "microsoft.extensions.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "path": "microsoft.extensions.configuration.binder/9.0.6", "hashPath": "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.1", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "path": "microsoft.extensions.hosting.abstractions/8.0.1", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6I7L2lNIziR93fjxecmeXi/dTnsGc7zHp+zXIywA01wzy769o11Ba6IDmese3MLXYBohiWf5X/dhT04AeouSHA==", "path": "microsoft.extensions.localization.abstractions/8.0.1", "hashPath": "microsoft.extensions.localization.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lCgpxE5r6v43SB40/yUVnSWZUUqUZF5iUWizhkx4gqvq0L0rMw5g8adWKGO7sfIaSbCiU0et85sDQWswhLcceg==", "path": "microsoft.extensions.logging.configuration/9.0.6", "hashPath": "microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-L1O0M3MrqGlkrPYMLzcCphQpCG0lSHfTSPrm1otALNBzTPiO8rxxkjhBIIa2onKv92UP30Y4QaiigVMTx8YcxQ==", "path": "microsoft.extensions.logging.console/9.0.6", "hashPath": "microsoft.extensions.logging.console.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "path": "microsoft.extensions.options.configurationextensions/9.0.6", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.Kiota.Abstractions/1.16.4": {"type": "package", "serviceable": true, "sha512": "sha512-pLQcH9tYRPA3CCsrqFUajUUaR8UmsqPmQExigpOfdaFvUXuMauUcCqxVnRGVnN002HqtOdsc/zKVewj6tFD6Dg==", "path": "microsoft.kiota.abstractions/1.16.4", "hashPath": "microsoft.kiota.abstractions.1.16.4.nupkg.sha512"}, "Microsoft.Kiota.Http.HttpClientLibrary/1.16.4": {"type": "package", "serviceable": true, "sha512": "sha512-5qf2xS5gXgO842V/tTiNqEt5VaMENWm23KMznl8SUl+UWkBeN55KKY0abRrQrw56igkwJJG6ReBwZoPQtwqHDg==", "path": "microsoft.kiota.http.httpclientlibrary/1.16.4", "hashPath": "microsoft.kiota.http.httpclientlibrary.1.16.4.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Form/1.16.4": {"type": "package", "serviceable": true, "sha512": "sha512-P/fnIzYON3qArN3a3dFtSXKxG2hf1K8Iq8RLesQIc0naMwFDYq4SSE3JcRNJ9YQQUOHKcoeRdP29BpkCFkbrhg==", "path": "microsoft.kiota.serialization.form/1.16.4", "hashPath": "microsoft.kiota.serialization.form.1.16.4.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Json/1.16.4": {"type": "package", "serviceable": true, "sha512": "sha512-WXRv4dhjwDcky+27MX6Q4L96s38ChhPSqdgOENhDsEHAZiN/YYjq1IBvQk31fnxJraqyJR/IbG84c51B1gNhJw==", "path": "microsoft.kiota.serialization.json/1.16.4", "hashPath": "microsoft.kiota.serialization.json.1.16.4.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Multipart/1.16.4": {"type": "package", "serviceable": true, "sha512": "sha512-QgY2Ckeipd53H5dyPv9v8far2BAPMa4dVNnLFd/m3uS7nQWi9ammB3Aj/cBArIeUvG1bznahCf9Tx2bDdtPGhQ==", "path": "microsoft.kiota.serialization.multipart/1.16.4", "hashPath": "microsoft.kiota.serialization.multipart.1.16.4.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Text/1.16.4": {"type": "package", "serviceable": true, "sha512": "sha512-TkThf7kvYwcGLW9OlxCkfppjJzV/YOrUfZhK8YYKshPwfOrsw4AkH35oUx/seC9pxxzb9xKecMHJ2GstunD53Q==", "path": "microsoft.kiota.serialization.text/1.16.4", "hashPath": "microsoft.kiota.serialization.text.1.16.4.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "path": "microsoft.win32.systemevents/9.0.0", "hashPath": "microsoft.win32.systemevents.9.0.0.nupkg.sha512"}, "Microsoft.Windows.CsWin32/0.3.106": {"type": "package", "serviceable": true, "sha512": "sha512-Mx5fK7uN6fwLR4wUghs6//HonAnwPBNmC2oonyJVhCUlHS/r6SUS3NkBc3+gaQiv+0/9bqdj1oSCKQFkNI+21Q==", "path": "microsoft.windows.cswin32/0.3.106", "hashPath": "microsoft.windows.cswin32.0.3.106.nupkg.sha512"}, "Microsoft.Windows.SDK.Win32Docs/0.1.42-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-Z/9po23gUA9aoukirh2ItMU2ZS9++Js9Gdds9fu5yuMojDrmArvY2y+tq9985tR3cxFxpZO1O35Wjfo0khj5HA==", "path": "microsoft.windows.sdk.win32docs/0.1.42-alpha", "hashPath": "microsoft.windows.sdk.win32docs.0.1.42-alpha.nupkg.sha512"}, "Microsoft.Windows.SDK.Win32Metadata/60.0.34-preview": {"type": "package", "serviceable": true, "sha512": "sha512-TA3DUNi4CTeo+ItTXBnGZFt2159XOGSl0UOlG5vjDj4WHqZjhwYyyUnzOtrbCERiSaP2Hzg7otJNWwOSZgutyA==", "path": "microsoft.windows.sdk.win32metadata/60.0.34-preview", "hashPath": "microsoft.windows.sdk.win32metadata.60.0.34-preview.nupkg.sha512"}, "Microsoft.Windows.WDK.Win32Metadata/0.11.4-experimental": {"type": "package", "serviceable": true, "sha512": "sha512-bf5MCmUyZf0gBlYQjx9UpRAZWBkRndyt9XicR+UNLvAUAFTZQbu6YaX/sNKZlR98Grn0gydfh/yT4I3vc0AIQA==", "path": "microsoft.windows.wdk.win32metadata/0.11.4-experimental", "hashPath": "microsoft.windows.wdk.win32metadata.0.11.4-experimental.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Refit/7.2.22": {"type": "package", "serviceable": true, "sha512": "sha512-xzpjDvWTKaJkhRejrHI6E2WXs2CaJYWZTMICq5Jha4wlSVvR3dd9n3M4p3X7BQ6uw0LJSFiHlTYwzjQIbJdArg==", "path": "refit/7.2.22", "hashPath": "refit.7.2.22.nupkg.sha512"}, "SkiaSharp/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YpwE8w0XAwMmik/WPpGFNH3/qnK10KkN0k+UMKsOzTzuqzv00UBsJd8/y4HEj6hXvOM6nX3LurRdq3ahluliZw==", "path": "skiasharp/3.119.0-preview.1.2", "hashPath": "skiasharp.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-sIosdSfGsNovt2JVQ0B+XSkENbhAQtS9XWcetEgcwvsB1cayjpGbiD3z7M+K7hLejfhnjTTILmCEoFTkT7cXNQ==", "path": "skiasharp.nativeassets.linux/3.119.0-preview.1.2", "hashPath": "skiasharp.nativeassets.linux.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-9TbuZloRaFQt8lgaDIBs5CQrDf6h9ouLrYEetFAUiGQZBrJho5wxr/WjlLGAprubOJChxtLrFSe1adp9mT5C8g==", "path": "skiasharp.nativeassets.macos/3.119.0-preview.1.2", "hashPath": "skiasharp.nativeassets.macos.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-3kmbq9blotAvaOxptEryB0Zx+N5iaJqigumTJyEvPSlt/oRAh2YaEEJbKa/1nqOU8Yg3p+qs9aMphZyZdJFTSg==", "path": "skiasharp.nativeassets.webassembly/3.119.0-preview.1.2", "hashPath": "skiasharp.nativeassets.webassembly.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-j8myDNfWRQG/qzKbCuIVfmMFjmTA9zGXB0XC9T1/LVVeF7mGC5ljo77vSqtEVHtIZOEiy3MZOjCRbMZCRLkAwA==", "path": "skiasharp.nativeassets.win32/3.119.0-preview.1.2", "hashPath": "skiasharp.nativeassets.win32.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.Resources/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-VFsQ9plcE+o2nkXUBNsQyS203+aU98MxNqiV9XtepxwGN6ifYg1zapZM4XTYVWLr+vjGDSpuz88nRqGADp+aUg==", "path": "skiasharp.resources/3.119.0-preview.1.2", "hashPath": "skiasharp.resources.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.SceneGraph/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AGlVuq1L/1+xlTpWJ9paIzcqrSOhvEHuk/KlHqLPd2EaB/Ub/upHvrx3BtExMbKf4D4Fda0+UgOzlSl8UIg8cA==", "path": "skiasharp.scenegraph/3.119.0-preview.1.2", "hashPath": "skiasharp.scenegraph.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.Skottie/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-1sAxgz+kOUUxTYZZc9ouWbcyeV4IspkiWnKr3353S/XSn/U4eUILN+J0tK4zCb3S6lP8ie6XlQiAGzoAaOIoSA==", "path": "skiasharp.skottie/3.119.0-preview.1.2", "hashPath": "skiasharp.skottie.3.119.0-preview.1.2.nupkg.sha512"}, "SkiaSharp.Views.Uno.WinUI/3.119.0-preview.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-sHOovewGIxD408XO7rJaxrFdhYYv3gOGs9AtIpqu3vTciY0AvYlt7rdw9kn4HvgpedJRvvQc9YhdwUeER0C6UQ==", "path": "skiasharp.views.uno.winui/3.119.0-preview.1.2", "hashPath": "skiasharp.views.uno.winui.3.119.0-preview.1.2.nupkg.sha512"}, "Std.UriTemplate/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ix5VXZwLfolwVHyGTSSJl6KIJ2le6E9YjLdZBMS1Xxzw7VJankRvQW8JoUL69tEgfcw+0qjgWrlxANrhvS0QCQ==", "path": "std.uritemplate/2.0.1", "hashPath": "std.uritemplate.2.0.1.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Json/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-XymImdgljgVEkd9trFSKWlNJac1Hr+L9lwFLQSUUQfFMkm88AHI6DINAMhpoiUWQIPLIqEjR702eydUb4Qb3Ng==", "path": "system.json/4.7.1", "hashPath": "system.json.4.7.1.nupkg.sha512"}, "System.Linq.Async/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WbiYEedFZeM+psmMyoCt1AKbZppAZg8Eq1ZTQ+521fGNeXqlgJj0tZYV5n1LsKRO5osQuitYxGNuzPTy3213sg==", "path": "system.linq.async/4.0.0", "hashPath": "system.linq.async.4.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Tmds.DBus.Protocol/0.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-ScSMrUrrw8px4kK1Glh0fZv/HQUlg1078bNXNPfRPKQ3WbRzV9HpsydYEOgSoMK5LWICMf2bMwIFH0pGjxjcMA==", "path": "tmds.dbus.protocol/0.21.2", "hashPath": "tmds.dbus.protocol.0.21.2.nupkg.sha512"}, "Uno.Core.Extensions/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2IFxilOCmozAJ+zLHZAONWjOBi3i8Nr5ndAsTjbB6enG9NkrYC/lv8/GF6c5Sj3e0TmRSLXWjutEZGHwLxj3pg==", "path": "uno.core.extensions/4.1.1", "hashPath": "uno.core.extensions.4.1.1.nupkg.sha512"}, "Uno.Core.Extensions.Collections/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZJFsUkMQR0vL9VUiy/+QRoUmTRJkJsLbIatU5QMOTwQzHsCqTtkvm2DF31Z1UeXu10EH+1J/YXAXr0LS+nOw5A==", "path": "uno.core.extensions.collections/4.1.1", "hashPath": "uno.core.extensions.collections.4.1.1.nupkg.sha512"}, "Uno.Core.Extensions.Disposables/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-NSsolz0P5R5lpgtBx8yf9F8qfjuw4+wykGsr99JDkYkbZ+ZkD651uW0lbvl6/lbQgcSljVgSExMnOw7WonbyVQ==", "path": "uno.core.extensions.disposables/4.1.1", "hashPath": "uno.core.extensions.disposables.4.1.1.nupkg.sha512"}, "Uno.Core.Extensions.Equality/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-BvJIN/RELgUxZadN+HO1PpvV0i3JA4sXgjlUtMin8gOr/zAoSQLeYWWhcVm+s5LqWj7oYGguqN3kw+peKUI0+w==", "path": "uno.core.extensions.equality/4.1.1", "hashPath": "uno.core.extensions.equality.4.1.1.nupkg.sha512"}, "Uno.Core.Extensions.Logging/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-f4nnnXbdiJWKKOfo1uJnZcAl36GvTuuuyG80VKA5nfKB7UpBMU1d/lB70TcOYESSBpT17xowmwcyB8hb6+i9ZA==", "path": "uno.core.extensions.logging/4.0.1", "hashPath": "uno.core.extensions.logging.4.0.1.nupkg.sha512"}, "Uno.Core.Extensions.Logging.Singleton/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P+MQuWFpfDzKNs4UMqQp0WxBGl2MAsxUZrWbdC6rFUWs1ZLCH8B8u8nbIloTnMniPOut3YYdTQHPNLhhTUYrzg==", "path": "uno.core.extensions.logging.singleton/4.1.1", "hashPath": "uno.core.extensions.logging.singleton.4.1.1.nupkg.sha512"}, "Uno.Diagnostics.Eventing/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Hd03em+Bga+w+ljCtiyc+713PQDtCB0Qkw3sBggvXXdDB2URwEc3N4TnM3vFeXpmHHZGM7dN7D8qxfiGoEEHzw==", "path": "uno.diagnostics.eventing/2.0.1", "hashPath": "uno.diagnostics.eventing.2.0.1.nupkg.sha512"}, "Uno.Dsp.Tasks/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Kb+S32ulgJGjprmoQW93GSu/T5emcmNtyK5Y/hJN54ovbEfK4S/NItxgJKjCRtQ2W9jJZxW8WwoxadnDTdAk5A==", "path": "uno.dsp.tasks/1.4.0", "hashPath": "uno.dsp.tasks.1.4.0.nupkg.sha512"}, "Uno.Extensions.Authentication/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-9I9Qkew02jE90+pCnhtUDdykE/ahrylydNgeH7OGwb7mbNaKtRc8bSAkDYFi1RhdzJUGM2pdy2qtqj69adiIvw==", "path": "uno.extensions.authentication/6.0.12", "hashPath": "uno.extensions.authentication.6.0.12.nupkg.sha512"}, "Uno.Extensions.Configuration/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-1Ir7PF1K7Hl154qnu9iBHf7f2jWmTDctd0Nz/52cN7jwKePr/qX0eyVipTp0dg80yAqK1Vg7fKSvln9ge9GODQ==", "path": "uno.extensions.configuration/6.0.12", "hashPath": "uno.extensions.configuration.6.0.12.nupkg.sha512"}, "Uno.Extensions.Core/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-30IfNQvj4nN7IZOKlZo/SBdwYOl2NZMvu3TcDlvvizkb8OlWuBfM7H1XMAfx+7iHakusDwofzVOYUO4xzTgWBw==", "path": "uno.extensions.core/6.0.12", "hashPath": "uno.extensions.core.6.0.12.nupkg.sha512"}, "Uno.Extensions.Core.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-GTyEIS0E35Y6XimWN3c0Khv/LSFIOUFdLl0RHjcxr1IXXrkL3TIF8cRzCQZe7QqsEu7U9ZVk0dMq7wrjtBkFeg==", "path": "uno.extensions.core.winui/6.0.12", "hashPath": "uno.extensions.core.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Hosting/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-6WtLJisut8wWTbIidTUcH+qL33TsTF7ldw2Li7j2tIISDrS2OtXj5NrljO3syH5eQc15gHGSHm+iMmYdm+/ffA==", "path": "uno.extensions.hosting/6.0.12", "hashPath": "uno.extensions.hosting.6.0.12.nupkg.sha512"}, "Uno.Extensions.Hosting.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-dhrw/nKevKoLE8XhqBXM4KXbrG6d0TBw2mVJ3fBOATuQ5yj0/PWkdqmIPFVAkhuoSM4aMSkOso8dtsva0AXE0g==", "path": "uno.extensions.hosting.winui/6.0.12", "hashPath": "uno.extensions.hosting.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Http/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-QxnFc/FZRmVFrwPMGBSsxrTMX3Irb6n/9uY2lUYav7aiSYrbrflbap9k7OGw/+NVkEn97NrE5mMRdLQ4f/gjOQ==", "path": "uno.extensions.http/6.0.12", "hashPath": "uno.extensions.http.6.0.12.nupkg.sha512"}, "Uno.Extensions.Http.Kiota/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-/7TmFcjpZWBfB8mwW3RuuQA2lPesWD9CAld970Ly6p7Ba9+Aoa8xFyfN6ajAIlAWPgZ6eAWJiK+WyIlpjIa9jA==", "path": "uno.extensions.http.kiota/6.0.12", "hashPath": "uno.extensions.http.kiota.6.0.12.nupkg.sha512"}, "Uno.Extensions.Http.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-ooWhQJYSquEa9VyKbwZdRfl+Y7rTfwOJPNglTVNTHFCTRe3gCepKcCEwKszhQgRCjfq/T9jqtI/jXJI3M5DUfg==", "path": "uno.extensions.http.winui/6.0.12", "hashPath": "uno.extensions.http.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Localization/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-JE0PzYqzQfnMSzr2E0SF3gkE0JCcRmXR3N3yYno+PAL9y8PeMV8AnhjAr910Ld1W3Os3aKGfqk4AjE7cpJ2DcA==", "path": "uno.extensions.localization/6.0.12", "hashPath": "uno.extensions.localization.6.0.12.nupkg.sha512"}, "Uno.Extensions.Localization.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-P0h1G0gshUBbvcQEHNF2oxpdmRi36/qs1rO/ToFEwHeWv/Thp1j8Hvp4XqdQ42I1gpKIuREBhfW+Cx4BWt7T0w==", "path": "uno.extensions.localization.winui/6.0.12", "hashPath": "uno.extensions.localization.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Logging.WebAssembly.Console/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VuDnbM+a/+OkTGvPxQPeYPeZLxWQxKrE55YqwSDxw8lWyo/XwFYrIQmYKTFR8f8/ldrs/Po82YSwfxLnX0he8g==", "path": "uno.extensions.logging.webassembly.console/1.7.0", "hashPath": "uno.extensions.logging.webassembly.console.1.7.0.nupkg.sha512"}, "Uno.Extensions.Logging.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-NKeHZ5LcFXXEM6H6Rf950w2k88pvR4E9QQWxNmOp8SktYxVCnP15ZdDXXdqUF07Z2t6m6Q/UvNv3o55M+QFAKQ==", "path": "uno.extensions.logging.winui/6.0.12", "hashPath": "uno.extensions.logging.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Navigation/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-ZCMOcfOgJfmw6sGg0cYH/CCBkKOgkIOPe4GcTN6Sp5ARMxQrXTiVGpZtZ1EB4i5oSd0laySVu7cvO4JD9Vs5kA==", "path": "uno.extensions.navigation/6.0.12", "hashPath": "uno.extensions.navigation.6.0.12.nupkg.sha512"}, "Uno.Extensions.Navigation.Toolkit.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-kSPUPDpaZWa9aHv/a/uDw4klqGV5I1ZaM8iQ57p/qBKC42mGCvMTLIGRJE2w78beF0pOIs4TPUUuwcKPGLGMhQ==", "path": "uno.extensions.navigation.toolkit.winui/6.0.12", "hashPath": "uno.extensions.navigation.toolkit.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Navigation.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-KcBzV3LXPzLwcS4l7ZyYWQI3YVXCbmZyaE3LEbJe81vEOZCxTSRzjNkMuRzFdiKS75ixwVzmSXC1iIsm3q7iUg==", "path": "uno.extensions.navigation.winui/6.0.12", "hashPath": "uno.extensions.navigation.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Reactive/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-/r1QyWdVjekz9yu6//gumVAkEsxsGPqmSQry4y6Csi8c1qhfBENjcIuLAZbR1EJ8Bw6fO3mOKkNkU+zakuAXsA==", "path": "uno.extensions.reactive/6.0.12", "hashPath": "uno.extensions.reactive.6.0.12.nupkg.sha512"}, "Uno.Extensions.Reactive.Messaging/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-cqOLFojEPkeV2v9B8tPC6nm0b9hg2UqHnXAa4aRIZLR/hrCMmQ2KMmGcUAnMeGImQZLe1Y10jD5ZcNLDj0XLRw==", "path": "uno.extensions.reactive.messaging/6.0.12", "hashPath": "uno.extensions.reactive.messaging.6.0.12.nupkg.sha512"}, "Uno.Extensions.Reactive.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-Y0mt2RkHwF03F3HRVCjzijSE0LM99u3sAgj7jHyT0j5bSPkhnO4xHYiulOQqPf/9ZHJWeqRJf0JRmJHyUbVJ+w==", "path": "uno.extensions.reactive.winui/6.0.12", "hashPath": "uno.extensions.reactive.winui.6.0.12.nupkg.sha512"}, "Uno.Extensions.Serialization/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-FCi35LidQ0gAW+BqzbIRlhLkGH9NGbAi9udvYAWMQtw3XIq+hxU3OkavKW6Iu/b1c9XU1HTE5OkTpaVZXmKswQ==", "path": "uno.extensions.serialization/6.0.12", "hashPath": "uno.extensions.serialization.6.0.12.nupkg.sha512"}, "Uno.Extensions.Serialization.Http/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-gZpSzblLgM5z9EzBwbDBUFbL1O6UMXrNqDWxe5MmYTuU9pkXwGfHLP1BpLpLucbELZqpr1xsAm5HIPBGXX25CA==", "path": "uno.extensions.serialization.http/6.0.12", "hashPath": "uno.extensions.serialization.http.6.0.12.nupkg.sha512"}, "Uno.Extensions.Serialization.Refit/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-W6ZceQLCwpzI1qlWHpd01uDsLkOfZFskT+WlKhnbUSc/VMS2ToJhQGKAe7bg5cmdBPZvlkZObgVYOiA3i13SEg==", "path": "uno.extensions.serialization.refit/6.0.12", "hashPath": "uno.extensions.serialization.refit.6.0.12.nupkg.sha512"}, "Uno.Extensions.Storage/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-wXZ1Gojabo7CqZSWXRCJEQ/1aBJygVPmaKANQ8zJKbgR1b0oLaNXBtOFOiiyx6MzKeEbbiTNHhl0ojoqApyJZQ==", "path": "uno.extensions.storage/6.0.12", "hashPath": "uno.extensions.storage.6.0.12.nupkg.sha512"}, "Uno.Extensions.Storage.WinUI/6.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-P+DBRfIpy7B3gtO4tu1fy9IOHbNme4l+HoxksW4lgdSCeLbmaIynx5T+bi+AToftdYMSGSjV2UVxEbw/759VxQ==", "path": "uno.extensions.storage.winui/6.0.12", "hashPath": "uno.extensions.storage.winui.6.0.12.nupkg.sha512"}, "Uno.Fonts.Fluent/2.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-mV1JWlpU9e6uWf/hamfTv+ysSRlRjjao1WtewGUtFz2/tB83C5h10gZReiWX4ZQXG2P0bgI/yx1ZxPYfiM3Hcg==", "path": "uno.fonts.fluent/2.6.1", "hashPath": "uno.fonts.fluent.2.6.1.nupkg.sha512"}, "Uno.Fonts.OpenSans/2.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-sFZUzCtp7K95kw2BPeFvuNF3RgBgFGAmjkPFGkSwZJwcSk368PW76gCaVYrRBe4niQHOsQg84xmE+PtoYvpVog==", "path": "uno.fonts.opensans/2.7.1", "hashPath": "uno.fonts.opensans.2.7.1.nupkg.sha512"}, "Uno.Fonts.Roboto/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZwpuV0++EPb7qAZuk8rj9fB4DR8FjeAcjug/JRYYf45p2tep6yb2GGxhU0yMGG+57Dtjavomq+abOXetSKEKvw==", "path": "uno.fonts.roboto/2.2.2", "hashPath": "uno.fonts.roboto.2.2.2.nupkg.sha512"}, "Uno.Foundation/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-qS+o3wL6DwYP37avSn7BJAkMD2hBCv0pFRrnB4v/q57+S7BvEaR/fDs28X/bEXi3eUjR48ggm2j3LWoGkNqNLg==", "path": "uno.foundation/6.0.797", "hashPath": "uno.foundation.6.0.797.nupkg.sha512"}, "Uno.Foundation.Logging/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-SoS/z8zSwXK3YPrXNOwsy5N0wI24KnSdx5IF6OswqAAJdebgm7RrPMfADPG02T28I01eruoPtxT9qfGfMjFdTQ==", "path": "uno.foundation.logging/6.0.797", "hashPath": "uno.foundation.logging.6.0.797.nupkg.sha512"}, "Uno.Material.WinUI/5.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-0VMTW4pRKfPZZbT0lozGLI2ejZ7kMQwaPdAzLZ6KY8tUgCup2ROlw2cTcjkN6dOK+RxbMx2IKICT97gRTK1yCg==", "path": "uno.material.winui/5.5.4", "hashPath": "uno.material.winui.5.5.4.nupkg.sha512"}, "Uno.Resizetizer/1.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-TeHaEA2v5SN7cr98BAN8Tfm7xKKpTwzL9KBFF8+FzK9Vfc8tuOw1mwetorc8nArXN9kseXufyZIfqWk+kEKeAQ==", "path": "uno.resizetizer/1.8.1", "hashPath": "uno.resizetizer.1.8.1.nupkg.sha512"}, "Uno.Sdk.Extras/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-slDSWKFsOJHljhR/WEy00Xk/g541MC9bPkZQv8ajniCz8F+jb7bveNmYxt0zrJB+Exp3j77PqWdB2oAOZcJDeA==", "path": "uno.sdk.extras/5.6.3", "hashPath": "uno.sdk.extras.5.6.3.nupkg.sha512"}, "Uno.Settings.DevServer/1.3.12": {"type": "package", "serviceable": true, "sha512": "sha512-k8Q0vIogOlbvOi0u1mlCfTJEFi9qjhsW3gouOn4zGX/b6xobmKhfD5/LSqnG+7udMHa3xSlmWPm8HDrJzaZJ2A==", "path": "uno.settings.devserver/1.3.12", "hashPath": "uno.settings.devserver.1.3.12.nupkg.sha512"}, "Uno.Themes.WinUI/5.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-UBJLUMKHztw8VZ0tZnNa6uVfqZH0NUgEFmLB0RwVSbC4tARZdintfx6xKbd49vQThYX4eCIutmLltT5/jWSnag==", "path": "uno.themes.winui/5.5.4", "hashPath": "uno.themes.winui.5.5.4.nupkg.sha512"}, "Uno.Toolkit/7.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-6VtUs6YGRrDna0YI3x+idmkiB2HAIGgop8/XueG41R1LHpqSEuRKqGgE52DYdUKfvSk9FB6O1jjH2hYuKtrvGA==", "path": "uno.toolkit/7.0.7", "hashPath": "uno.toolkit.7.0.7.nupkg.sha512"}, "Uno.Toolkit.WinUI/7.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-Jm0zEmyxJEUghQyq9brYxXx9eP7hbM84KKKdp85jbc7TAhXqLRIiTQN+de48JJW1D2ho8nkU6wGlToF8I6bHHg==", "path": "uno.toolkit.winui/7.0.7", "hashPath": "uno.toolkit.winui.7.0.7.nupkg.sha512"}, "Uno.Toolkit.WinUI.Material/7.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-F2oXcSv1gtodoJXYy23XiRQTfFA6s0Q97CtXqVbIQAtRHaINoNp4ftGO09DQgO77DVgXmezUmrTVkUp2etPwGA==", "path": "uno.toolkit.winui.material/7.0.7", "hashPath": "uno.toolkit.winui.material.7.0.7.nupkg.sha512"}, "Uno.UI.Adapter.Microsoft.Extensions.Logging/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-lqJYtyBtgtaDOccQLnKAe6+v0gGlg/KLO285s/LEejUqFLyRbWS/3rIPPjW7rEJpL1exGiZTYzmJ2LqIRlW82A==", "path": "uno.ui.adapter.microsoft.extensions.logging/6.0.797", "hashPath": "uno.ui.adapter.microsoft.extensions.logging.6.0.797.nupkg.sha512"}, "Uno.UI.HotDesign/1.13.6": {"type": "package", "serviceable": true, "sha512": "sha512-xzTHxUpCvFTSxvdwVYGCCDdKn08gZnGP/yLWnD0ly8RwPL7of5Qlw0umK9B+3BKWOyy/F8i3Aa70Rff2P2/IpQ==", "path": "uno.ui.hotdesign/1.13.6", "hashPath": "uno.ui.hotdesign.1.13.6.nupkg.sha512"}, "Uno.Wasm.WebSockets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-U2d297Z/CxP0GZ3mYCu8kdKzO7PKw5lE7j+gwgXAZS1OlZiWS9EoiBbpUmr/0nSH6wPqClMbtGi6LLs0jslOog==", "path": "uno.wasm.websockets/1.1.0", "hashPath": "uno.wasm.websockets.1.1.0.nupkg.sha512"}, "Uno.WinRT/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-QZYUdcTZb3qcJnDsFCH/lifi9V5STOsTegX+WAbxV3kMi9mlXYlgUwesa61BGQ0LNoRplYY3xEGafF3dBGQZNQ==", "path": "uno.winrt/6.0.797", "hashPath": "uno.winrt.6.0.797.nupkg.sha512"}, "Uno.WinUI/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-LPt346D8gWVazXzanIxKgG5oSnyr2xsPRXtC2npAwPivkLwDOrkU1+zqqJWKfXTpWMJ2EAeykBOzdP4expXc7A==", "path": "uno.winui/6.0.797", "hashPath": "uno.winui.6.0.797.nupkg.sha512"}, "Uno.WinUI.DevServer/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-q9pQIAwdo6JQdIOR5b3ZzDoisYbZyKc7qQUZvG24Q+388zSOfnYmU3tYyeb4owTt5NF2EMgqNBPPxDgUKBiT8Q==", "path": "uno.winui.devserver/6.0.797", "hashPath": "uno.winui.devserver.6.0.797.nupkg.sha512"}, "Uno.WinUI.DevServer.Messaging/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-fGnVT/yfwe7+61E3ct4G+zI8yjPPsBgKmrcYHNY1P6luBgvw/FIMGH+AQ1zgAXSO5ZFvTBsTK8YeurUoVnZVug==", "path": "uno.winui.devserver.messaging/6.0.797", "hashPath": "uno.winui.devserver.messaging.6.0.797.nupkg.sha512"}, "Uno.WinUI.Graphics2DSK/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-hYZW31HfD/3exidgcuLTKshd1jqFpT3UnIost9Wb5gyLvCXUWqZLkoTvmew+JwHyZUMtNwWl8Kp4xFJ/tNeFwA==", "path": "uno.winui.graphics2dsk/6.0.797", "hashPath": "uno.winui.graphics2dsk.6.0.797.nupkg.sha512"}, "Uno.WinUI.Lottie/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-DZHXjQDh9UvsyCs1XzeB6unGx/IIOoYioCl7awAjexZAXRw/Uqxn1jhircSdwyNSOequBYCa1evOXc/zqOugdA==", "path": "uno.winui.lottie/6.0.797", "hashPath": "uno.winui.lottie.6.0.797.nupkg.sha512"}, "Uno.WinUI.Runtime.Skia/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-j7TvBlshRRz0n5FNPJq88so7RrxJ5CQikceSlQ0ycXefQr6HeK6EFaAg+wt3E303rquT4MrCaaNOpbfi9JgRng==", "path": "uno.winui.runtime.skia/6.0.797", "hashPath": "uno.winui.runtime.skia.6.0.797.nupkg.sha512"}, "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-vYFVL71tMmwPlQjNUxhD3PVu8uqKz9/toO09VY5KsifJoawyTXT0tNg3hEq46k2+SVFFfEh5blIf0tT7oIj9hw==", "path": "uno.winui.runtime.skia.linux.framebuffer/6.0.797", "hashPath": "uno.winui.runtime.skia.linux.framebuffer.6.0.797.nupkg.sha512"}, "Uno.WinUI.Runtime.Skia.MacOS/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-R7noFjkmtQS+TNwO7YcK30oA6ma3YlngNbVllq2vEJi46/NPFDr3GpdXR0EDCiNchfIgX8BJ5yfSR9DPmYqzrg==", "path": "uno.winui.runtime.skia.macos/6.0.797", "hashPath": "uno.winui.runtime.skia.macos.6.0.797.nupkg.sha512"}, "Uno.WinUI.Runtime.Skia.Win32/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-GRlIEcmTyTqMbX8TOlwcWSxXOyeVlBm6opsPEtwHigZMNobwAZvsDzh7PZgB+RjdLNj4vbmacAHp7SG7viLlVQ==", "path": "uno.winui.runtime.skia.win32/6.0.797", "hashPath": "uno.winui.runtime.skia.win32.6.0.797.nupkg.sha512"}, "Uno.WinUI.Runtime.Skia.Wpf/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-dCrWolicDiVkG/nlM0tNlBKDlmbXY852jxXw3RzeHhk1znONONfKtvFtWsCEGodekscQagARcdt5XVT6EjUA2g==", "path": "uno.winui.runtime.skia.wpf/6.0.797", "hashPath": "uno.winui.runtime.skia.wpf.6.0.797.nupkg.sha512"}, "Uno.WinUI.Runtime.Skia.X11/6.0.797": {"type": "package", "serviceable": true, "sha512": "sha512-uyUoenlCfKmIeID05BC+arM91tL4GVdkPFq4qN4QQpWikq0dap48vT/PeJzo4SeEmxNvsPsopu9rjaK+Dn7oQQ==", "path": "uno.winui.runtime.skia.x11/6.0.797", "hashPath": "uno.winui.runtime.skia.x11.6.0.797.nupkg.sha512"}}}