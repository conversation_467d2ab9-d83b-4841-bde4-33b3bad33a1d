<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net9.0-desktop</TargetFrameworks>

    <OutputType>WinExe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>UnoSkiaApp</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.unoskiaapp</ApplicationId>
    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <!-- Package Publisher -->
    <ApplicationPublisher>UnoSkiaApp</ApplicationPublisher>
    <!-- Package Description -->
    <Description>UnoSkiaApp powered by Uno Platform.</Description>

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Material;
      Dsp;
      Hosting;
      Toolkit;
      Logging;
      MVUX;
      Configuration;
      HttpKiota;
      Serialization;
      Localization;
      Navigation;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
  </PropertyGroup>

</Project>
