﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(TargetFramework)' == 'net9.0-desktop' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)\uno.winui\6.0.797\buildTransitive\net8.0\uno.winui.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui\6.0.797\buildTransitive\net8.0\uno.winui.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.winui.runtime.skia.x11\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.X11.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui.runtime.skia.x11\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.X11.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.winui.runtime.skia.wpf\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.Wpf.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui.runtime.skia.wpf\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.Wpf.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.winui.runtime.skia.win32\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.Win32.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui.runtime.skia.win32\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.Win32.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.winui.runtime.skia.macos\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.MacOS.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui.runtime.skia.macos\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.MacOS.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.winui.runtime.skia.linux.framebuffer\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.Linux.FrameBuffer.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui.runtime.skia.linux.framebuffer\6.0.797\buildTransitive\Uno.WinUI.Runtime.Skia.Linux.FrameBuffer.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.winui.lottie\6.0.797\buildTransitive\Uno.WinUI.Lottie.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui.lottie\6.0.797\buildTransitive\Uno.WinUI.Lottie.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.winui.devserver\6.0.797\buildTransitive\Uno.WinUI.DevServer.targets" Condition="Exists('$(NuGetPackageRoot)\uno.winui.devserver\6.0.797\buildTransitive\Uno.WinUI.DevServer.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.extensions.options\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.extensions.options\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.extensions.logging.abstractions\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.extensions.logging.abstractions\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.toolkit.winui\7.0.7\buildTransitive\Uno.Toolkit.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\uno.toolkit.winui\7.0.7\buildTransitive\Uno.Toolkit.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.themes.winui\5.5.4\buildTransitive\Uno.Themes.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\uno.themes.winui\5.5.4\buildTransitive\Uno.Themes.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\communitytoolkit.mvvm\8.4.0\buildTransitive\CommunityToolkit.Mvvm.targets" Condition="Exists('$(NuGetPackageRoot)\communitytoolkit.mvvm\8.4.0\buildTransitive\CommunityToolkit.Mvvm.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.material.winui\5.5.4\buildTransitive\Uno.Material.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\uno.material.winui\5.5.4\buildTransitive\Uno.Material.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.toolkit.winui.material\7.0.7\buildTransitive\Uno.Toolkit.WinUI.Material.targets" Condition="Exists('$(NuGetPackageRoot)\uno.toolkit.winui.material\7.0.7\buildTransitive\Uno.Toolkit.WinUI.Material.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.settings.devserver\1.3.12\buildTransitive\Uno.Settings.DevServer.targets" Condition="Exists('$(NuGetPackageRoot)\uno.settings.devserver\1.3.12\buildTransitive\Uno.Settings.DevServer.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.sdk.extras\5.6.3\buildTransitive\Uno.Sdk.Extras.targets" Condition="Exists('$(NuGetPackageRoot)\uno.sdk.extras\5.6.3\buildTransitive\Uno.Sdk.Extras.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.resizetizer\1.8.1\build\Uno.Resizetizer.targets" Condition="Exists('$(NuGetPackageRoot)\uno.resizetizer\1.8.1\build\Uno.Resizetizer.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.fonts.opensans\2.7.1\buildTransitive\net7.0\Uno.Fonts.OpenSans.targets" Condition="Exists('$(NuGetPackageRoot)\uno.fonts.opensans\2.7.1\buildTransitive\net7.0\Uno.Fonts.OpenSans.targets')" />
    <Import Project="$(NuGetPackageRoot)\system.text.json\8.0.5\buildTransitive\net6.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)\system.text.json\8.0.5\buildTransitive\net6.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.extensions.configuration.binder\9.0.6\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.extensions.configuration.binder\9.0.6\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.core\6.0.12\buildTransitive\Uno.Extensions.Core.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.core\6.0.12\buildTransitive\Uno.Extensions.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.serialization\6.0.12\buildTransitive\Uno.Extensions.Serialization.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.serialization\6.0.12\buildTransitive\Uno.Extensions.Serialization.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.storage\6.0.12\buildTransitive\Uno.Extensions.Storage.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.storage\6.0.12\buildTransitive\Uno.Extensions.Storage.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.hosting\6.0.12\buildTransitive\Uno.Extensions.Hosting.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.hosting\6.0.12\buildTransitive\Uno.Extensions.Hosting.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.configuration\6.0.12\buildTransitive\Uno.Extensions.Configuration.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.configuration\6.0.12\buildTransitive\Uno.Extensions.Configuration.targets')" />
    <Import Project="$(NuGetPackageRoot)\skiasharp.nativeassets.webassembly\3.119.0-preview.1.2\buildTransitive\netstandard1.0\SkiaSharp.NativeAssets.WebAssembly.targets" Condition="Exists('$(NuGetPackageRoot)\skiasharp.nativeassets.webassembly\3.119.0-preview.1.2\buildTransitive\netstandard1.0\SkiaSharp.NativeAssets.WebAssembly.targets')" />
    <Import Project="$(NuGetPackageRoot)\skiasharp.views.uno.winui\3.119.0-preview.1.2\buildTransitive\net8.0\SkiaSharp.Views.Uno.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\skiasharp.views.uno.winui\3.119.0-preview.1.2\buildTransitive\net8.0\SkiaSharp.Views.Uno.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.storage.winui\6.0.12\buildTransitive\Uno.Extensions.Storage.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.storage.winui\6.0.12\buildTransitive\Uno.Extensions.Storage.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\refit\7.2.22\buildTransitive\netstandard2.0\refit.targets" Condition="Exists('$(NuGetPackageRoot)\refit\7.2.22\buildTransitive\netstandard2.0\refit.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.serialization.refit\6.0.12\buildTransitive\Uno.Extensions.Serialization.Refit.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.serialization.refit\6.0.12\buildTransitive\Uno.Extensions.Serialization.Refit.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.http\6.0.12\buildTransitive\Uno.Extensions.Http.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.http\6.0.12\buildTransitive\Uno.Extensions.Http.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.serialization.http\6.0.12\buildTransitive\Uno.Extensions.Serialization.Http.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.serialization.http\6.0.12\buildTransitive\Uno.Extensions.Serialization.Http.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.reactive\6.0.12\buildTransitive\Uno.Extensions.Reactive.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.reactive\6.0.12\buildTransitive\Uno.Extensions.Reactive.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.reactive.winui\6.0.12\buildTransitive\Uno.Extensions.Reactive.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.reactive.winui\6.0.12\buildTransitive\Uno.Extensions.Reactive.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.reactive.messaging\6.0.12\buildTransitive\Uno.Extensions.Reactive.Messaging.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.reactive.messaging\6.0.12\buildTransitive\Uno.Extensions.Reactive.Messaging.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.navigation\6.0.12\buildTransitive\Uno.Extensions.Navigation.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.navigation\6.0.12\buildTransitive\Uno.Extensions.Navigation.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.navigation.winui\6.0.12\buildTransitive\Uno.Extensions.Navigation.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.navigation.winui\6.0.12\buildTransitive\Uno.Extensions.Navigation.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.navigation.toolkit.winui\6.0.12\buildTransitive\Uno.Extensions.Navigation.Toolkit.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.navigation.toolkit.winui\6.0.12\buildTransitive\Uno.Extensions.Navigation.Toolkit.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.localization\6.0.12\buildTransitive\Uno.Extensions.Localization.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.localization\6.0.12\buildTransitive\Uno.Extensions.Localization.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.extensions.authentication\6.0.12\buildTransitive\Uno.Extensions.Authentication.targets" Condition="Exists('$(NuGetPackageRoot)\uno.extensions.authentication\6.0.12\buildTransitive\Uno.Extensions.Authentication.targets')" />
    <Import Project="$(NuGetPackageRoot)\uno.dsp.tasks\1.4.0\build\Uno.Dsp.Tasks.targets" Condition="Exists('$(NuGetPackageRoot)\uno.dsp.tasks\1.4.0\build\Uno.Dsp.Tasks.targets')" />
  </ImportGroup>
</Project>