is_global = true
build_property.PackageReferenceProperty = Uno.WinUI,Uno.<PERSON><PERSON><PERSON>,Uno.Sdk.Extras,Uno.Settings.DevServer,Uno.UI.HotDesign,Uno.WinUI.DevServer,Uno.UI.Adapter.Microsoft.Extensions.Logging,Microsoft.Extensions.Logging.Console,Uno.Toolkit.WinUI,Uno.Toolkit.WinUI.Material,Uno.Material.WinUI,Uno.Dsp.Tasks,Uno.Fonts.OpenSans,Uno.Extensions.Configuration,Uno.Extensions.Core.WinUI,Uno.Extensions.Hosting.WinUI,Uno.Extensions.Http.WinUI,Uno.Extensions.Http.Kiota,Uno.Extensions.Localization.WinUI,Uno.Extensions.Logging.WinUI,Uno.Extensions.Navigation.WinUI,Uno.Extensions.Navigation.Toolkit.WinUI,Uno.Extensions.Reactive.WinUI,Uno.Extensions.Reactive.Messaging,Uno.Extensions.Serialization.Http,Uno.Extensions.Serialization.Refit,Uno.Win<PERSON>.<PERSON>,SkiaSharp.Views.Uno.WinUI,SkiaSharp.<PERSON><PERSON>,Uno.WinUI.Graphics2DSK,Uno.WinUI.Runtime.Skia.Linux.FrameBuffer,Uno.WinUI.Runtime.Skia.MacOS,Uno.WinUI.Runtime.Skia.Win32,Uno.WinUI.Runtime.Skia.Wpf,Uno.WinUI.Runtime.Skia.X11
build_property.ProjectReferenceProperty = 
build_property.UnoSdkVersion = 6.0.146
build_property.HotDesignUnoAssets = 
build_property.UnoExtensionsGeneration_DisableDynamicallyAccessedMembersAttribute = 
build_property.UnoExtensionsGeneration_DisableMaybeNullAttribute = 
build_property.UnoExtensionsGeneration_DisableMaybeNullWhenAttribute = 
build_property.UnoExtensionsGeneration_DisableMemberNotNullAttribute = 
build_property.UnoExtensionsGeneration_DisableMemberNotNullWhenAttribute = 
build_property.UnoExtensionsGeneration_DisableNotNullIfNotNullAttribute = 
build_property.UnoExtensionsGeneration_DisableNotNullWhenAttribute = 
build_property.UnoExtensionsGeneration_DisableMetadataUpdateHandlerAttribute = 
build_property.UnoExtensionsGeneration_DisableCreateNewOnMetadataUpdateAttribute = 
build_property.UnoExtensionsGeneration_DisableIsExternalInit = 
build_property.UnoExtensionsGeneration_DisableMetadataUpdateOriginalTypeAttribute = 
build_property.UnoExtensionsGeneration_DisableModuleInitializerAttribute = 
build_property.RefitInternalNamespace = UnoSkiaApp
build_property.XamlSourceGeneratorTracingFolder = 
build_property.TargetFramework = net9.0-desktop
build_property.TargetFramework = net9.0-desktop
build_property.ProjectTypeGuidsProperty = 
build_property.MSBuildProjectFullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\UnoSkiaApp.csproj
build_property.RootNamespace = UnoSkiaApp
build_property.RootNamespace = UnoSkiaApp
build_property.DefineConstantsProperty = TRACE,DISABLE_XAML_GENERATED_BREAK_ON_UNHANDLED_EXCEPTION,DEBUG,NET,NET9_0,NETCOREAPP,DESKTOP,DESKTOP1_0,NET5_0_OR_GREATER,NET6_0_OR_GREATER,NET7_0_OR_GREATER,NET8_0_OR_GREATER,NET9_0_OR_GREATER,NETCOREAPP1_0_OR_GREATER,NETCOREAPP1_1_OR_GREATER,NETCOREAPP2_0_OR_GREATER,NETCOREAPP2_1_OR_GREATER,NETCOREAPP2_2_OR_GREATER,NETCOREAPP3_0_OR_GREATER,NETCOREAPP3_1_OR_GREATER,DESKTOP1_0_OR_GREATER,HAS_UNO,__UNO__,HAS_UNO_WINUI,__UNO_WINUI__,WINUI_WINDOWING,UNO_HAS_FRAMEWORKELEMENT_MEASUREOVERRIDE,UNO_HAS_NO_IDEPENDENCYOBJECT,UNO_REFERENCE_API,HAS_UNO_SKIA,HAS_UNO_SKIA_X11,__UNO_SKIA__,__UNO_SKIA_X11__,UNO_REFERENCE_API,HAS_UNO_SKIA,HAS_UNO_SKIA_WPF,__UNO_SKIA__,__UNO_SKIA_WPF__,UNO_REFERENCE_API,HAS_UNO_SKIA,HAS_UNO_SKIA_WIN32,__UNO_SKIA__,__UNO_SKIA_WIN32__,UNO_REFERENCE_API,HAS_UNO_SKIA,HAS_UNO_SKIA_MACOS,__UNO_SKIA__,__UNO_SKIA_MACOS__,UNO_REFERENCE_API,HAS_UNO_SKIA,HAS_UNO_SKIA_LINUX_FB,__UNO_SKIA__,__UNO_SKIA_LINUX_FB__
build_property.Configuration = Debug
build_property.IntermediateOutputPath = obj\Debug\net9.0-desktop\
build_property.AndroidApplication = 
build_property.OutputType = WinExe
build_property.MSBuildProjectName = UnoSkiaApp
build_property.BuildingProject = false
build_property.DesignTimeBuild = true
build_property.UnoUISourceGeneratorDebuggerBreak = 
build_property.BuildingInsideVisualStudio = 
build_property.UnoPlatformIDE = 
build_property.IsHotReloadHost = 
build_property.UnoGenerateXamlSourcesProvider = 
build_property.UnoForceHotReloadCodeGen = 
build_property.UnoForceIncludeProjectConfiguration = 
build_property.UnoForceIncludeServerProcessorsConfiguration = 
build_property.IsUnoHead = true
build_property.IsUnoHead = true
build_property.UnoDisableHotRestartHelperGeneration = 
build_property.RuntimeIdentifier = 
build_property.UnoRuntimeIdentifier = Skia
build_property.UnoWinRTRuntimeIdentifier = 
build_property.UnoUIRuntimeIdentifier = 
build_property.UnoHotReloadMode = 
build_property.SolutionFileName = UnoSkiaApp.sln
build_property.LangName = en-US
build_property.LangID = 1033
build_property.SolutionDir = Q:\test\unotest3\UnoSkiaApp\
build_property.SolutionExt = .sln
build_property.UseHostCompilerIfAvailable = true
build_property.DefineExplicitDefaults = true
build_property.Platform = AnyCPU
build_property.SolutionPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp.sln
build_property.SolutionName = UnoSkiaApp
build_property.VSIDEResolvedNonMSBuildProjectOutputs = 
build_property.DevEnvDir = *Undefined*
build_property.MSBuildVersion = 17.14.5
build_property.UseWPF = 
build_property.UnoRemoteControlPort = 
build_property.UnoRemoteControlHost = 
build_property.UnoRemoteControlProcessorsPath = q:\.tools\.nuget\packages\uno.winui.devserver\6.0.797\buildTransitive\../tools/rc/processors
build_property.UnoRemoteControlConfigCookie = 
build_property.UnoHotReloadDiagnosticsLogPath = 
build_property.AppendRuntimeIdentifierToOutputPath = true
build_property.OutputPath = bin\Debug\net9.0-desktop\
build_property.UnoDisableBindableTypeProvidersGeneration = 
build_property.ShouldWriteErrorOnInvalidXaml = 
build_property.IsUiAutomationMappingEnabled = 
build_property.ShouldAnnotateGeneratedXaml = 
build_property.DefaultLanguage = en
build_property.UnoPlatformTelemetryOptOut = 
build_property.UnoPlatformDefaultSymbolsFontFamily = ms-appx:///Uno.Fonts.Fluent/Fonts/uno-fluentui-assets.ttf
build_property.ExcludeXamlNamespacesProperty = android,ios,macos,wasm,not_skia,not_netstdref,androidskia,iosskia,tvosskia,wasmskia,unittests,win,not_mux
build_property.IncludeXamlNamespacesProperty = not_android,not_ios,not_macos,not_wasm,skia,netstdref,not_androidskia,not_iosskia,not_tvosskia,not_wasmskia,xamarin,not_win,legacy
build_property.XamlGeneratorAnalyzerSuppressionsProperty = csharp-618 // Ignore obsolete members warnings,csharp-105 // Ignore duplicate namespaces,csharp-1591 // Ignore missing XML comment warnings,csharp-CS8669 // Ignore annotation for nullable reference types,csharp-CS9113 // Parameter is unread
build_property.UnoEnableXamlFuzzyMatching = 
build_property.UnoXamlResourcesTrimming = 
build_property.BaseIntermediateOutputPath = obj\
build_property.AssemblyName = UnoSkiaApp
build_property.UnoDefaultFont = OpenSans
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows,desktop
build_property.ProjectDir = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Presentation/MainPage.xaml]
build_metadata.AdditionalFiles.SourceItemGroup = Page
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Presentation\MainPage.xaml
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Presentation\MainPage.xaml
build_metadata.AdditionalFiles.XamlRuntime = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Presentation/SecondPage.xaml]
build_metadata.AdditionalFiles.SourceItemGroup = Page
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Presentation\SecondPage.xaml
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Presentation\SecondPage.xaml
build_metadata.AdditionalFiles.XamlRuntime = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Presentation/Shell.xaml]
build_metadata.AdditionalFiles.SourceItemGroup = Page
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Presentation\Shell.xaml
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Presentation\Shell.xaml
build_metadata.AdditionalFiles.XamlRuntime = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Styles/ColorPaletteOverride.xaml]
build_metadata.AdditionalFiles.SourceItemGroup = Page
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Styles\ColorPaletteOverride.xaml
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Styles\ColorPaletteOverride.xaml
build_metadata.AdditionalFiles.XamlRuntime = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/App.xaml]
build_metadata.AdditionalFiles.SourceItemGroup = ApplicationDefinition
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\App.xaml
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\App.xaml
build_metadata.AdditionalFiles.XamlRuntime = WinUI

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Strings/en/Resources.resw]
build_metadata.AdditionalFiles.SourceItemGroup = PRIResource
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\en\Resources.resw
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\en\Resources.resw
build_metadata.AdditionalFiles.XamlRuntime = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Strings/es/Resources.resw]
build_metadata.AdditionalFiles.SourceItemGroup = PRIResource
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\es\Resources.resw
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\es\Resources.resw
build_metadata.AdditionalFiles.XamlRuntime = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Strings/fr/Resources.resw]
build_metadata.AdditionalFiles.SourceItemGroup = PRIResource
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\fr\Resources.resw
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\fr\Resources.resw
build_metadata.AdditionalFiles.XamlRuntime = 

[Q:/test/unotest3/UnoSkiaApp/UnoSkiaApp/Strings/pt-BR/Resources.resw]
build_metadata.AdditionalFiles.SourceItemGroup = PRIResource
build_metadata.AdditionalFiles.Link = 
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.FullPath = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\pt-BR\Resources.resw
build_metadata.AdditionalFiles.Identity = Q:\test\unotest3\UnoSkiaApp\UnoSkiaApp\Strings\pt-BR\Resources.resw
build_metadata.AdditionalFiles.XamlRuntime = 
