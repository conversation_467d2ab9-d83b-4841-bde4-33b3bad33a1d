{"runtimeOptions": {"tfm": "net9.0", "framework": {"name": "Microsoft.NETCore.App", "version": "9.0.0"}, "configProperties": {"Switch.System.Windows.Media.EnableHardwareAccelerationInRdp": true, "Windows.ApplicationModel.DataTransfer.DragDrop.ExternalSupport": true, "MVVMTOOLKIT_ENABLE_INOTIFYPROPERTYCHANGING_SUPPORT": true, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false}}}