{"format": 1, "restore": {"Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\UnoSkiaApp.csproj": {}}, "projects": {"Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\UnoSkiaApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\UnoSkiaApp.csproj", "projectName": "UnoSkiaApp", "projectPath": "Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\UnoSkiaApp.csproj", "packagesPath": "q:\\.tools\\.nuget\\packages", "outputPath": "Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-desktop"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-desktop1.0": {"targetAlias": "net9.0-desktop", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1507"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-desktop1.0": {"targetAlias": "net9.0-desktop", "dependencies": {"Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.6, )", "autoReferenced": true}, "SkiaSharp.Skottie": {"target": "Package", "version": "[3.119.0-preview.1.2, )", "autoReferenced": true}, "SkiaSharp.Views.Uno.WinUI": {"target": "Package", "version": "[3.119.0-preview.1.2, )", "autoReferenced": true}, "Uno.Dsp.Tasks": {"target": "Package", "version": "[1.4.0, )", "autoReferenced": true}, "Uno.Extensions.Configuration": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Core.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Hosting.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Http.Kiota": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Http.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Localization.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Logging.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Navigation.Toolkit.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Navigation.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Reactive.Messaging": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Reactive.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Serialization.Http": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Serialization.Refit": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Fonts.OpenSans": {"target": "Package", "version": "[2.7.1, )", "autoReferenced": true}, "Uno.Material.WinUI": {"target": "Package", "version": "[5.5.4, )", "autoReferenced": true}, "Uno.Resizetizer": {"suppressParent": "All", "target": "Package", "version": "[1.8.1, )", "autoReferenced": true}, "Uno.Sdk.Extras": {"suppressParent": "All", "target": "Package", "version": "[5.6.3, )", "autoReferenced": true}, "Uno.Settings.DevServer": {"suppressParent": "All", "target": "Package", "version": "[1.3.12, )", "autoReferenced": true}, "Uno.Toolkit.WinUI": {"target": "Package", "version": "[7.0.7, )", "autoReferenced": true}, "Uno.Toolkit.WinUI.Material": {"target": "Package", "version": "[7.0.7, )", "autoReferenced": true}, "Uno.UI.Adapter.Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.UI.HotDesign": {"target": "Package", "version": "[1.13.6, )", "autoReferenced": true}, "Uno.WinUI": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.DevServer": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Graphics2DSK": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Lottie": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.MacOS": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.Win32": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.Wpf": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.X11": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}