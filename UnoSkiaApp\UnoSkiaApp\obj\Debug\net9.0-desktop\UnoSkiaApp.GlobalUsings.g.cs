// <auto-generated/>
global using global::Microsoft.UI.Xaml;
global using global::Microsoft.UI.Xaml.Controls;
global using global::Microsoft.UI.Xaml.Media;
global using global::Microsoft.UI.Xaml.Navigation;
global using global::System;
global using global::System.Collections.Generic;
global using global::System.IO;
global using global::System.Linq;
global using global::System.Net.Http;
global using global::System.Threading;
global using global::System.Threading.Tasks;
global using global::System.Windows.Input;
global using global::Uno.Extensions;
global using global::Uno.Extensions.Authentication;
global using global::Uno.Extensions.Configuration;
global using global::Uno.Extensions.DependencyInjection;
global using global::Uno.Extensions.Diagnostics;
global using global::Uno.Extensions.Edition;
global using global::Uno.Extensions.Equality;
global using global::Uno.Extensions.Hosting;
global using global::Uno.Extensions.Http;
global using global::Uno.Extensions.Localization;
global using global::Uno.Extensions.Logging;
global using global::Uno.Extensions.Navigation;
global using global::Uno.Extensions.Navigation.Navigators;
global using global::Uno.Extensions.Navigation.Toolkit;
global using global::Uno.Extensions.Navigation.Toolkit.Controls;
global using global::Uno.Extensions.Navigation.Toolkit.Navigators;
global using global::Uno.Extensions.Navigation.UI;
global using global::Uno.Extensions.Navigation.UI.Controls;
global using global::Uno.Extensions.Reactive;
global using global::Uno.Extensions.Reactive.Messaging;
global using global::Uno.Extensions.Reactive.UI;
global using global::Uno.Extensions.Serialization;
global using global::Uno.Extensions.Serialization.Http;
global using global::Uno.Extensions.Serialization.Refit;
global using global::Uno.Extensions.Storage;
global using global::Uno.Extensions.Storage.KeyValueStorage;
global using global::Uno.Extensions.Toolkit;
global using global::Uno.Material;
global using global::Uno.Themes;
global using global::Uno.Toolkit;
global using global::Uno.Toolkit.UI;
global using global::Uno.Toolkit.UI.Material;
global using global::Uno.UI;
global using global::Windows.ApplicationModel;
global using global::Windows.Networking.Connectivity;
global using global::Windows.Storage;
