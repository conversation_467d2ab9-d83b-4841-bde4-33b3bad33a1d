{"version": "2.0.0", "tasks": [{"label": "build-desktop", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/UnoSkiaApp/UnoSkiaApp.csproj", "/property:GenerateFullPaths=true", "/property:TargetFramework=net9.0-desktop", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish-desktop", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/UnoSkiaApp/UnoSkiaApp.csproj", "/property:GenerateFullPaths=true", "/property:TargetFramework=net9.0-desktop", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}]}