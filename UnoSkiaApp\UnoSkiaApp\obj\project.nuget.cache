{"version": 2, "dgSpecHash": "ir7m48GSzK4=", "success": true, "projectFilePath": "q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\UnoSkiaApp.csproj", "expectedPackageFiles": ["q:\\.tools\\.nuget\\packages\\commonservicelocator\\2.0.5\\commonservicelocator.2.0.5.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\communitytoolkit.mvvm\\8.4.0\\communitytoolkit.mvvm.8.4.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\harfbuzzsharp\\8.3.1.1-preview.1.2\\harfbuzzsharp.8.3.1.1-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\harfbuzzsharp.nativeassets.linux\\8.3.1.1-preview.1.2\\harfbuzzsharp.nativeassets.linux.8.3.1.1-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\harfbuzzsharp.nativeassets.macos\\8.3.1.1-preview.1.2\\harfbuzzsharp.nativeassets.macos.8.3.1.1-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\harfbuzzsharp.nativeassets.win32\\8.3.1.1-preview.1.2\\harfbuzzsharp.nativeassets.win32.8.3.1.1-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\libvlcsharp\\3.7.0\\libvlcsharp.3.7.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.6\\microsoft.extensions.configuration.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.6\\microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.6\\microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\8.0.0\\microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\8.0.0\\microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.0\\microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.0\\microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\8.0.0\\microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.6\\microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.6\\microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.diagnostics\\8.0.0\\microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.1\\microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.hosting\\8.0.0\\microsoft.extensions.hosting.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.1\\microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.http\\8.0.0\\microsoft.extensions.http.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\8.0.1\\microsoft.extensions.localization.abstractions.8.0.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.logging\\9.0.6\\microsoft.extensions.logging.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.6\\microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.6\\microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.logging.console\\9.0.6\\microsoft.extensions.logging.console.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.logging.debug\\8.0.0\\microsoft.extensions.logging.debug.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\8.0.0\\microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\8.0.0\\microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.options\\9.0.6\\microsoft.extensions.options.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.6\\microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.6\\microsoft.extensions.primitives.9.0.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.kiota.abstractions\\1.16.4\\microsoft.kiota.abstractions.1.16.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.kiota.http.httpclientlibrary\\1.16.4\\microsoft.kiota.http.httpclientlibrary.1.16.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.kiota.serialization.form\\1.16.4\\microsoft.kiota.serialization.form.1.16.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.kiota.serialization.json\\1.16.4\\microsoft.kiota.serialization.json.1.16.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.kiota.serialization.multipart\\1.16.4\\microsoft.kiota.serialization.multipart.1.16.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.kiota.serialization.text\\1.16.4\\microsoft.kiota.serialization.text.1.16.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.win32.systemevents\\9.0.0\\microsoft.win32.systemevents.9.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.windows.cswin32\\0.3.106\\microsoft.windows.cswin32.0.3.106.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.windows.sdk.win32docs\\0.1.42-alpha\\microsoft.windows.sdk.win32docs.0.1.42-alpha.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.windows.sdk.win32metadata\\60.0.34-preview\\microsoft.windows.sdk.win32metadata.60.0.34-preview.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\microsoft.windows.wdk.win32metadata\\0.11.4-experimental\\microsoft.windows.wdk.win32metadata.0.11.4-experimental.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\refit\\7.2.22\\refit.7.2.22.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp\\3.119.0-preview.1.2\\skiasharp.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.nativeassets.linux\\3.119.0-preview.1.2\\skiasharp.nativeassets.linux.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.nativeassets.macos\\3.119.0-preview.1.2\\skiasharp.nativeassets.macos.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.nativeassets.webassembly\\3.119.0-preview.1.2\\skiasharp.nativeassets.webassembly.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.nativeassets.win32\\3.119.0-preview.1.2\\skiasharp.nativeassets.win32.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.resources\\3.119.0-preview.1.2\\skiasharp.resources.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.scenegraph\\3.119.0-preview.1.2\\skiasharp.scenegraph.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.skottie\\3.119.0-preview.1.2\\skiasharp.skottie.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\skiasharp.views.uno.winui\\3.119.0-preview.1.2\\skiasharp.views.uno.winui.3.119.0-preview.1.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\std.uritemplate\\2.0.1\\std.uritemplate.2.0.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.diagnostics.eventlog\\8.0.0\\system.diagnostics.eventlog.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.json\\4.7.1\\system.json.4.7.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.linq.async\\4.0.0\\system.linq.async.4.0.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.text.json\\8.0.5\\system.text.json.8.0.5.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\tmds.dbus.protocol\\0.21.2\\tmds.dbus.protocol.0.21.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.core.extensions\\4.1.1\\uno.core.extensions.4.1.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.core.extensions.collections\\4.1.1\\uno.core.extensions.collections.4.1.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.core.extensions.disposables\\4.1.1\\uno.core.extensions.disposables.4.1.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.core.extensions.equality\\4.1.1\\uno.core.extensions.equality.4.1.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.core.extensions.logging\\4.0.1\\uno.core.extensions.logging.4.0.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.core.extensions.logging.singleton\\4.1.1\\uno.core.extensions.logging.singleton.4.1.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.diagnostics.eventing\\2.0.1\\uno.diagnostics.eventing.2.0.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.dsp.tasks\\1.4.0\\uno.dsp.tasks.1.4.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.authentication\\6.0.12\\uno.extensions.authentication.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.configuration\\6.0.12\\uno.extensions.configuration.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.core\\6.0.12\\uno.extensions.core.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.core.winui\\6.0.12\\uno.extensions.core.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.hosting\\6.0.12\\uno.extensions.hosting.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.hosting.winui\\6.0.12\\uno.extensions.hosting.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.http\\6.0.12\\uno.extensions.http.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.http.kiota\\6.0.12\\uno.extensions.http.kiota.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.http.winui\\6.0.12\\uno.extensions.http.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.localization\\6.0.12\\uno.extensions.localization.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.localization.winui\\6.0.12\\uno.extensions.localization.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.logging.webassembly.console\\1.7.0\\uno.extensions.logging.webassembly.console.1.7.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.logging.winui\\6.0.12\\uno.extensions.logging.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.navigation\\6.0.12\\uno.extensions.navigation.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.navigation.toolkit.winui\\6.0.12\\uno.extensions.navigation.toolkit.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.navigation.winui\\6.0.12\\uno.extensions.navigation.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.reactive\\6.0.12\\uno.extensions.reactive.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.reactive.messaging\\6.0.12\\uno.extensions.reactive.messaging.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.reactive.winui\\6.0.12\\uno.extensions.reactive.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.serialization\\6.0.12\\uno.extensions.serialization.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.serialization.http\\6.0.12\\uno.extensions.serialization.http.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.serialization.refit\\6.0.12\\uno.extensions.serialization.refit.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.storage\\6.0.12\\uno.extensions.storage.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.extensions.storage.winui\\6.0.12\\uno.extensions.storage.winui.6.0.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.fonts.fluent\\2.6.1\\uno.fonts.fluent.2.6.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.fonts.opensans\\2.7.1\\uno.fonts.opensans.2.7.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.fonts.roboto\\2.2.2\\uno.fonts.roboto.2.2.2.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.foundation\\6.0.797\\uno.foundation.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.foundation.logging\\6.0.797\\uno.foundation.logging.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.material.winui\\5.5.4\\uno.material.winui.5.5.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.resizetizer\\1.8.1\\uno.resizetizer.1.8.1.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.sdk.extras\\5.6.3\\uno.sdk.extras.5.6.3.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.settings.devserver\\1.3.12\\uno.settings.devserver.1.3.12.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.themes.winui\\5.5.4\\uno.themes.winui.5.5.4.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.toolkit\\7.0.7\\uno.toolkit.7.0.7.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.toolkit.winui\\7.0.7\\uno.toolkit.winui.7.0.7.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.toolkit.winui.material\\7.0.7\\uno.toolkit.winui.material.7.0.7.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.ui.adapter.microsoft.extensions.logging\\6.0.797\\uno.ui.adapter.microsoft.extensions.logging.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.ui.hotdesign\\1.13.6\\uno.ui.hotdesign.1.13.6.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.wasm.websockets\\1.1.0\\uno.wasm.websockets.1.1.0.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winrt\\6.0.797\\uno.winrt.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui\\6.0.797\\uno.winui.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.devserver\\6.0.797\\uno.winui.devserver.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.devserver.messaging\\6.0.797\\uno.winui.devserver.messaging.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.graphics2dsk\\6.0.797\\uno.winui.graphics2dsk.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.lottie\\6.0.797\\uno.winui.lottie.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.runtime.skia\\6.0.797\\uno.winui.runtime.skia.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.runtime.skia.linux.framebuffer\\6.0.797\\uno.winui.runtime.skia.linux.framebuffer.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.runtime.skia.macos\\6.0.797\\uno.winui.runtime.skia.macos.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.runtime.skia.win32\\6.0.797\\uno.winui.runtime.skia.win32.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.runtime.skia.wpf\\6.0.797\\uno.winui.runtime.skia.wpf.6.0.797.nupkg.sha512", "q:\\.tools\\.nuget\\packages\\uno.winui.runtime.skia.x11\\6.0.797\\uno.winui.runtime.skia.x11.6.0.797.nupkg.sha512"], "logs": []}