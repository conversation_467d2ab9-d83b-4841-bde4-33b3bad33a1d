using System.Diagnostics;
using System.Runtime.InteropServices;
using Uno.UI.Hosting;

namespace UnoSkiaApp;

internal class Program
{
    private const string MutexName = "UnoSkiaApp_SingleInstance_Mutex";

    [STAThread]
    public static void Main(string[] args)
    {
        // Check if another instance is already running
        using var mutex = new Mutex(true, MutexName, out bool createdNew);

        if (!createdNew)
        {
            // Another instance is already running, try to bring it to foreground
            BringExistingInstanceToForeground();
            return;
        }

        var host = UnoPlatformHostBuilder.Create()
            .App(() => new App())
            .UseX11()
            .UseLinuxFrameBuffer()
            .UseMacOS()
            .UseWin32()
            .Build();

        host.Run();
    }

    private static void BringExistingInstanceToForeground()
    {
        try
        {
            // Find the existing process
            var currentProcess = Process.GetCurrentProcess();
            var processes = Process.GetProcessesByName(currentProcess.ProcessName);

            foreach (var process in processes)
            {
                if (process.Id != currentProcess.Id && !string.IsNullOrEmpty(process.MainWindowTitle))
                {
                    // Bring the existing window to foreground
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        ShowWindow(process.MainWindowHandle, SW_RESTORE);
                        SetForegroundWindow(process.MainWindowHandle);
                    }
                    break;
                }
            }
        }
        catch
        {
            // If we can't bring the existing instance to foreground, just exit silently
        }
    }

    // Windows API declarations for bringing window to foreground
    [DllImport("user32.dll")]
    private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

    [DllImport("user32.dll")]
    private static extern bool SetForegroundWindow(IntPtr hWnd);

    private const int SW_RESTORE = 9;
}
