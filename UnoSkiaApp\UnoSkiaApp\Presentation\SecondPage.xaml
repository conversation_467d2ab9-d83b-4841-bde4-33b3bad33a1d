﻿<Page x:Class="UnoSkiaApp.Presentation.SecondPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="using:UnoSkiaApp.Presentation"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:uen="using:Uno.Extensions.Navigation.UI"
      xmlns:utu="using:Uno.Toolkit.UI"
      xmlns:um="using:Uno.Material"
      Background="{ThemeResource BackgroundBrush}">

  <Grid utu:SafeArea.Insets="VisibleBounds">
    <Grid.RowDefinitions>
      <RowDefinition Height="Auto" />
      <RowDefinition />
    </Grid.RowDefinitions>
    <utu:NavigationBar Content="Second Page" />
    <StackPanel Grid.Row="1"
          HorizontalAlignment="Center"
          VerticalAlignment="Center">
      <TextBlock Text="{Binding Entity.Name}"
        HorizontalAlignment="Center"
        VerticalAlignment="Center"
        Margin="8" />
    </StackPanel>

  </Grid>
</Page>
