﻿global using System.Collections.Immutable;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Localization;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using UnoSkiaApp.Models;
global using UnoSkiaApp.Presentation;
global using UnoSkiaApp.Services.Endpoints;
global using UnoSkiaApp.DataContracts;
global using UnoSkiaApp.DataContracts.Serialization;
global using UnoSkiaApp.Services.Caching;
global using UnoSkiaApp.Client;
global using Uno.Extensions.Http.Kiota;
global using ApplicationExecutionState = Windows.ApplicationModel.Activation.ApplicationExecutionState;
[assembly: Uno.Extensions.Reactive.Config.BindableGenerationTool(3)]
