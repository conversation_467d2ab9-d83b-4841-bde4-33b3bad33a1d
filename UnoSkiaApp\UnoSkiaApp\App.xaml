<Application x:Class="UnoSkiaApp.App"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:utum="using:Uno.Toolkit.UI.Material">

  <Application.Resources>
    <ResourceDictionary>
      <ResourceDictionary.MergedDictionaries>
        <!-- Load WinUI resources -->
        <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
        <utum:MaterialToolkitTheme
          ColorOverrideSource="ms-appx:///Styles/ColorPaletteOverride.xaml">
          <!-- NOTE: You can override the default Roboto font by providing your font assets here. -->
          <!-- <utum:MaterialToolkitTheme.FontOverrideDictionary>
            <ResourceDictionary>
              <FontFamily x:Key="MaterialLightFontFamily">ms-appx:///Uno.Fonts.Roboto/Fonts/Roboto-Light.ttf#Roboto</FontFamily>
              <FontFamily x:Key="MaterialMediumFontFamily">ms-appx:///Uno.Fonts.Roboto/Fonts/Roboto-Medium.ttf#Roboto</FontFamily>
              <FontFamily x:Key="MaterialRegularFontFamily">ms-appx:///Uno.Fonts.Roboto/Fonts/Roboto-Regular.ttf#Roboto</FontFamily>
            </ResourceDictionary>
          </utum:MaterialToolkitTheme.FontOverrideDictionary> -->
        </utum:MaterialToolkitTheme>
      </ResourceDictionary.MergedDictionaries>

      <!-- Add resources here -->

    </ResourceDictionary>
  </Application.Resources>

</Application>
