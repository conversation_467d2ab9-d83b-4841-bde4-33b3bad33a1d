{"version": 3, "targets": {"net9.0-desktop1.0": {"CommonServiceLocator/2.0.5": {"type": "package", "compile": {"lib/netcoreapp3.0/CommonServiceLocator.dll": {}}, "runtime": {"lib/netcoreapp3.0/CommonServiceLocator.dll": {}}}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "HarfBuzzSharp/8.3.1.1-preview.1.2": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "8.3.1.1-preview.1.2", "HarfBuzzSharp.NativeAssets.macOS": "8.3.1.1-preview.1.2"}, "compile": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1-preview.1.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-loongarch64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-loongarch64"}, "runtimes/linux-musl-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-loongarch64"}, "runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-riscv64"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-riscv64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-riscv64"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x86"}}}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1-preview.1.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1-preview.1.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "LibVLCSharp/3.7.0": {"type": "package", "compile": {"lib/net6.0/LibVLCSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/LibVLCSharp.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Kiota.Abstractions/1.16.4": {"type": "package", "dependencies": {"Std.UriTemplate": "2.0.1"}, "compile": {"lib/net8.0/Microsoft.Kiota.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Kiota.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Kiota.Http.HttpClientLibrary/1.16.4": {"type": "package", "dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "compile": {"lib/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"related": ".xml"}}}, "Microsoft.Kiota.Serialization.Form/1.16.4": {"type": "package", "dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "compile": {"lib/net8.0/Microsoft.Kiota.Serialization.Form.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Form.dll": {"related": ".xml"}}}, "Microsoft.Kiota.Serialization.Json/1.16.4": {"type": "package", "dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "compile": {"lib/net8.0/Microsoft.Kiota.Serialization.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Json.dll": {"related": ".xml"}}}, "Microsoft.Kiota.Serialization.Multipart/1.16.4": {"type": "package", "dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "compile": {"lib/net8.0/Microsoft.Kiota.Serialization.Multipart.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Multipart.dll": {"related": ".xml"}}}, "Microsoft.Kiota.Serialization.Text/1.16.4": {"type": "package", "dependencies": {"Microsoft.Kiota.Abstractions": "1.16.4"}, "compile": {"lib/net8.0/Microsoft.Kiota.Serialization.Text.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Text.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Windows.CsWin32/0.3.106": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.Win32Docs": "0.1.42-alpha", "Microsoft.Windows.SDK.Win32Metadata": "60.0.34-preview", "Microsoft.Windows.WDK.Win32Metadata": "0.11.4-experimental"}, "build": {"build/netstandard1.0/_._": {}}}, "Microsoft.Windows.SDK.Win32Docs/0.1.42-alpha": {"type": "package", "compile": {"lib/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".xml"}}, "build": {"buildTransitive/netstandard1.0/Microsoft.Windows.SDK.Win32Docs.props": {}}}, "Microsoft.Windows.SDK.Win32Metadata/60.0.34-preview": {"type": "package", "build": {"buildTransitive/netstandard1.0/Microsoft.Windows.SDK.Win32Metadata.props": {}}}, "Microsoft.Windows.WDK.Win32Metadata/0.11.4-experimental": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.Win32Metadata": "60.0.34-preview"}, "build": {"buildTransitive/netstandard1.0/Microsoft.Windows.WDK.Win32Metadata.props": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Refit/7.2.22": {"type": "package", "compile": {"lib/net8.0/Refit.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Refit.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/refit.props": {}, "buildTransitive/netstandard2.0/refit.targets": {}}}, "SkiaSharp/3.119.0-preview.1.2": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.macOS": "3.119.0-preview.1.2"}, "compile": {"ref/net8.0/SkiaSharp.dll": {}}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.Linux/3.119.0-preview.1.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-loongarch64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-loongarch64"}, "runtimes/linux-musl-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-loongarch64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-loongarch64"}, "runtimes/linux-musl-riscv64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-riscv64"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-riscv64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-riscv64"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x86"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0-preview.1.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.WebAssembly/3.119.0-preview.1.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets": {}}}, "SkiaSharp.NativeAssets.Win32/3.119.0-preview.1.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp.Resources/3.119.0-preview.1.2": {"type": "package", "dependencies": {"SkiaSharp": "3.119.0-preview.1.2"}, "compile": {"lib/net8.0/SkiaSharp.Resources.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.Resources.dll": {"related": ".pdb"}}}, "SkiaSharp.SceneGraph/3.119.0-preview.1.2": {"type": "package", "dependencies": {"SkiaSharp": "3.119.0-preview.1.2"}, "compile": {"lib/net8.0/SkiaSharp.SceneGraph.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.SceneGraph.dll": {"related": ".pdb"}}}, "SkiaSharp.Skottie/3.119.0-preview.1.2": {"type": "package", "dependencies": {"SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.Resources": "3.119.0-preview.1.2", "SkiaSharp.SceneGraph": "3.119.0-preview.1.2"}, "compile": {"lib/net8.0/SkiaSharp.Skottie.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.Skottie.dll": {"related": ".pdb"}}}, "SkiaSharp.Views.Uno.WinUI/3.119.0-preview.1.2": {"type": "package", "dependencies": {"SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.WebAssembly": "3.119.0-preview.1.2", "Uno.WinUI": "5.2.175"}, "compile": {"lib/net8.0/SkiaSharp.Views.Windows.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.Views.Windows.dll": {"related": ".pdb"}}, "build": {"buildTransitive/net8.0/SkiaSharp.Views.Uno.WinUI.targets": {}}}, "Std.UriTemplate/2.0.1": {"type": "package", "compile": {"lib/net5.0/Std.UriTemplate.dll": {}}, "runtime": {"lib/net5.0/Std.UriTemplate.dll": {}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Json/4.7.1": {"type": "package", "compile": {"lib/netstandard2.0/System.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Json.dll": {"related": ".xml"}}}, "System.Linq.Async/4.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Linq.Async.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Linq.Async.dll": {"related": ".xml"}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Json/8.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "Tmds.DBus.Protocol/0.21.2": {"type": "package", "dependencies": {"System.IO.Pipelines": "8.0.0"}, "compile": {"lib/net8.0/Tmds.DBus.Protocol.dll": {}}, "runtime": {"lib/net8.0/Tmds.DBus.Protocol.dll": {}}}, "Uno.Core.Extensions/4.1.1": {"type": "package", "compile": {"lib/net7.0/Uno.Core.Extensions.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/Uno.Core.Extensions.dll": {"related": ".pdb"}}}, "Uno.Core.Extensions.Collections/4.1.1": {"type": "package", "dependencies": {"Uno.Core.Extensions.Disposables": "4.1.1", "Uno.Core.Extensions.Equality": "4.1.1"}, "compile": {"lib/net7.0/Uno.Core.Extensions.Collections.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/Uno.Core.Extensions.Collections.dll": {"related": ".pdb"}}}, "Uno.Core.Extensions.Disposables/4.1.1": {"type": "package", "compile": {"lib/net7.0/Uno.Core.Extensions.Disposables.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/Uno.Core.Extensions.Disposables.dll": {"related": ".pdb"}}}, "Uno.Core.Extensions.Equality/4.1.1": {"type": "package", "compile": {"lib/net7.0/Uno.Core.Extensions.Equality.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/Uno.Core.Extensions.Equality.dll": {"related": ".pdb"}}}, "Uno.Core.Extensions.Logging/4.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "5.0.0"}, "compile": {"lib/net6.0/Uno.Core.Extensions.Logging.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/Uno.Core.Extensions.Logging.dll": {"related": ".pdb"}}}, "Uno.Core.Extensions.Logging.Singleton/4.1.1": {"type": "package", "dependencies": {"CommonServiceLocator": "2.0.5", "Microsoft.Extensions.Logging": "5.0.0"}, "compile": {"lib/net7.0/Uno.Core.Extensions.Logging.Singleton.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/Uno.Core.Extensions.Logging.Singleton.dll": {"related": ".pdb"}}}, "Uno.Diagnostics.Eventing/2.0.1": {"type": "package", "compile": {"lib/net5.0/Uno.Diagnostics.Eventing.dll": {"related": ".pdb"}}, "runtime": {"lib/net5.0/Uno.Diagnostics.Eventing.dll": {"related": ".pdb"}}}, "Uno.Dsp.Tasks/1.4.0": {"type": "package", "build": {"build/Uno.Dsp.Tasks.targets": {}}}, "Uno.Extensions.Authentication/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.Extensions.Storage": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Authentication.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Authentication.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Authentication.targets": {}}}, "Uno.Extensions.Configuration/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Serialization": "6.0.12", "Uno.Extensions.Storage": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Configuration.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Configuration.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Configuration.targets": {}}}, "Uno.Extensions.Core/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net8.0/Uno.Extensions.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Core.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Core.props": {}, "buildTransitive/Uno.Extensions.Core.targets": {}}}, "Uno.Extensions.Core.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Core.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Core.UI.dll": {"related": ".pdb;.xml"}}}, "Uno.Extensions.Hosting/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Hosting.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Hosting.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Hosting.targets": {}}}, "Uno.Extensions.Hosting.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Core.WinUI": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Storage": "6.0.12", "Uno.Extensions.Storage.WinUI": "6.0.12", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Hosting.WinUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Hosting.WinUI.dll": {"related": ".pdb;.xml"}}}, "Uno.Extensions.Http/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Http.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Http.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Http.targets": {}}}, "Uno.Extensions.Http.Kiota/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Kiota.Abstractions": "1.16.4", "Microsoft.Kiota.Http.HttpClientLibrary": "1.16.4", "Microsoft.Kiota.Serialization.Form": "1.16.4", "Microsoft.Kiota.Serialization.Json": "1.16.4", "Microsoft.Kiota.Serialization.Multipart": "1.16.4", "Microsoft.Kiota.Serialization.Text": "1.16.4", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Authentication": "6.0.12", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.Extensions.Serialization": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Http.Kiota.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Http.Kiota.dll": {"related": ".pdb;.xml"}}}, "Uno.Extensions.Http.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Http.WinUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Http.WinUI.dll": {"related": ".pdb;.xml"}}}, "Uno.Extensions.Localization/6.0.12": {"type": "package", "compile": {"lib/net8.0/Uno.Extensions.Localization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Localization.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Localization.targets": {}}}, "Uno.Extensions.Localization.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Localization": "6.0.12", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Localization.WinUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Localization.WinUI.dll": {"related": ".pdb;.xml"}}}, "Uno.Extensions.Logging.WebAssembly.Console/1.7.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "1.1.1"}, "compile": {"lib/net7.0/Uno.Extensions.Logging.WebAssembly.Console.dll": {}}, "runtime": {"lib/net7.0/Uno.Extensions.Logging.WebAssembly.Console.dll": {}}}, "Uno.Extensions.Logging.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Logging.WebAssembly.Console": "1.7.0", "Uno.UI.Adapter.Microsoft.Extensions.Logging": "6.0.465", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Logging.WinUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Logging.WinUI.dll": {"related": ".pdb;.xml"}}}, "Uno.Extensions.Navigation/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Hosting": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Navigation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Navigation.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Navigation.targets": {}}}, "Uno.Extensions.Navigation.Toolkit.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions": "4.1.1", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Core.WinUI": "6.0.12", "Uno.Extensions.Navigation.WinUI": "6.0.12", "Uno.Toolkit.WinUI": "7.0.2", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Navigation.Toolkit.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Navigation.Toolkit.UI.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Navigation.Toolkit.WinUI.targets": {}}}, "Uno.Extensions.Navigation.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions": "4.1.1", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Core.WinUI": "6.0.12", "Uno.Extensions.Hosting": "6.0.12", "Uno.Extensions.Hosting.WinUI": "6.0.12", "Uno.Extensions.Navigation": "6.0.12", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Navigation.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Navigation.UI.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Navigation.WinUI.targets": {}}}, "Uno.Extensions.Reactive/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Linq.Async": "4.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Toolkit": "7.0.7"}, "compile": {"lib/net8.0/Uno.Extensions.Reactive.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Reactive.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Reactive.targets": {}}}, "Uno.Extensions.Reactive.Messaging/6.0.12": {"type": "package", "dependencies": {"CommunityToolkit.Mvvm": "7.0.1", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Linq.Async": "4.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Reactive": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Reactive.Messaging.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Reactive.Messaging.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Reactive.Messaging.targets": {}}}, "Uno.Extensions.Reactive.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Async": "4.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Extensions.Reactive": "6.0.12", "Uno.Toolkit.WinUI": "7.0.2", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Reactive.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Reactive.UI.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Reactive.WinUI.targets": {}}}, "Uno.Extensions.Serialization/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Serialization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Serialization.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Serialization.targets": {}}}, "Uno.Extensions.Serialization.Http/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Http": "6.0.12", "Uno.Extensions.Serialization": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Serialization.Http.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Serialization.Http.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Serialization.Http.targets": {}}}, "Uno.Extensions.Serialization.Refit/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Refit": "7.2.22", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Serialization": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Serialization.Refit.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Serialization.Refit.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Serialization.Refit.targets": {}}}, "Uno.Extensions.Storage/6.0.12": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Serialization": "6.0.12"}, "compile": {"lib/net8.0/Uno.Extensions.Storage.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Uno.Extensions.Storage.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Storage.targets": {}}}, "Uno.Extensions.Storage.WinUI/6.0.12": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "SkiaSharp.Views.Uno.WinUI": "3.119.0-preview.1.2", "System.Collections.Immutable": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4", "Uno.Core.Extensions.Collections": "4.1.1", "Uno.Extensions.Configuration": "6.0.12", "Uno.Extensions.Core": "6.0.12", "Uno.Extensions.Serialization": "6.0.12", "Uno.Extensions.Storage": "6.0.12", "Uno.WinUI": "6.0.465", "Uno.WinUI.Graphics2DSK": "6.0.465", "Uno.WinUI.Lottie": "6.0.465"}, "compile": {"lib/net8.0-desktop1.0/Uno.Extensions.Storage.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-desktop1.0/Uno.Extensions.Storage.UI.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Uno.Extensions.Storage.WinUI.targets": {}}}, "Uno.Fonts.Fluent/2.6.1": {"type": "package", "compile": {"lib/netstandard1.0/Uno.Fonts.Fluent.dll": {"related": ".uprimarker"}}, "runtime": {"lib/netstandard1.0/Uno.Fonts.Fluent.dll": {"related": ".uprimarker"}}, "build": {"buildTransitive/Uno.Fonts.Fluent.props": {}}}, "Uno.Fonts.OpenSans/2.7.1": {"type": "package", "compile": {"lib/net7.0/Uno.Fonts.OpenSans.dll": {"related": ".uprimarker"}}, "runtime": {"lib/net7.0/Uno.Fonts.OpenSans.dll": {"related": ".uprimarker"}}, "build": {"buildTransitive/net7.0/Uno.Fonts.OpenSans.targets": {}}}, "Uno.Fonts.Roboto/2.2.2": {"type": "package", "compile": {"lib/netstandard2.0/Uno.Fonts.Roboto.dll": {"related": ".uprimarker"}}, "runtime": {"lib/netstandard2.0/Uno.Fonts.Roboto.dll": {"related": ".uprimarker"}}}, "Uno.Foundation/6.0.797": {"type": "package", "compile": {"lib/net9.0/Uno.Foundation.dll": {}}, "runtime": {"lib/net9.0/Uno.Foundation.dll": {}}, "build": {"buildTransitive/Uno.Foundation.props": {}}}, "Uno.Foundation.Logging/6.0.797": {"type": "package", "compile": {"lib/net8.0/Uno.Foundation.Logging.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Uno.Foundation.Logging.dll": {"related": ".pdb"}}}, "Uno.Material.WinUI/5.5.4": {"type": "package", "dependencies": {"Uno.Core.Extensions.Disposables": "4.0.1", "Uno.Core.Extensions.Logging.Singleton": "4.0.1", "Uno.Fonts.Roboto": "2.2.2", "Uno.Themes.WinUI": "5.5.4", "Uno.WinUI": "5.0.19", "Uno.WinUI.Lottie": "5.0.19"}, "compile": {"lib/net8.0/Uno.Material.WinUI.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Uno.Material.WinUI.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.Material.WinUI.targets": {}}}, "Uno.Resizetizer/1.8.1": {"type": "package", "build": {"build/Uno.Resizetizer.targets": {}}}, "Uno.Sdk.Extras/5.6.3": {"type": "package", "build": {"buildTransitive/Uno.Sdk.Extras.props": {}, "buildTransitive/Uno.Sdk.Extras.targets": {}}}, "Uno.Settings.DevServer/1.3.12": {"type": "package", "build": {"buildTransitive/Uno.Settings.DevServer.targets": {}}}, "Uno.Themes.WinUI/5.5.4": {"type": "package", "dependencies": {"Uno.Core.Extensions.Disposables": "4.0.1", "Uno.Core.Extensions.Logging.Singleton": "4.0.1", "Uno.Fonts.Roboto": "2.2.2", "Uno.WinUI": "5.0.19"}, "compile": {"lib/net8.0/Uno.Themes.WinUI.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Uno.Themes.WinUI.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.Themes.WinUI.targets": {}}}, "Uno.Toolkit/7.0.7": {"type": "package", "compile": {"lib/netstandard2.0/Uno.Toolkit.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Uno.Toolkit.dll": {"related": ".pdb"}}}, "Uno.Toolkit.WinUI/7.0.7": {"type": "package", "dependencies": {"Uno.Core.Extensions.Collections": "4.0.1", "Uno.Core.Extensions.Logging": "4.0.1", "Uno.Core.Extensions.Logging.Singleton": "4.0.1", "Uno.Toolkit": "7.0.7", "Uno.WinUI": "5.4.22"}, "compile": {"lib/net8.0/Uno.Toolkit.WinUI.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Uno.Toolkit.WinUI.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.Toolkit.WinUI.targets": {}}}, "Uno.Toolkit.WinUI.Material/7.0.7": {"type": "package", "dependencies": {"Uno.Material.WinUI": "5.0.13", "Uno.Toolkit.WinUI": "7.0.7", "Uno.WinUI": "5.4.22"}, "compile": {"lib/net8.0/Uno.Toolkit.WinUI.Material.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Uno.Toolkit.WinUI.Material.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.Toolkit.WinUI.Material.targets": {}}}, "Uno.UI.Adapter.Microsoft.Extensions.Logging/6.0.797": {"type": "package", "dependencies": {"Uno.Core.Extensions.Logging": "4.0.1", "Uno.Core.Extensions.Logging.Singleton": "4.0.1", "Uno.Foundation.Logging": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Adapter.Microsoft.Extensions.Logging.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Adapter.Microsoft.Extensions.Logging.dll": {"related": ".pdb"}}}, "Uno.UI.HotDesign/1.13.6": {"type": "package", "dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Microsoft.Extensions.Logging": "8.0.1", "Uno.Core.Extensions.Logging.Singleton": "4.1.1", "Uno.Themes.WinUI": "5.5.4", "Uno.Toolkit.WinUI": "7.0.7", "Uno.WinUI": "6.0.780", "Uno.WinUI.DevServer": "6.0.780", "Uno.WinUI.DevServer.Messaging": "6.0.780"}, "compile": {"lib/net8.0/Uno.UI.HotDesign.Abstractions.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Client.Core.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Client.dll": {"related": ".Core.pdb;.pdb"}, "lib/net8.0/Uno.UI.HotDesign.CommunityToolkit.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Hierarchy.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Messaging.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.PropertyGrid.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Toolbox.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactions.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactivity.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.dll": {"related": ".Interactions.pdb;.Interactivity.pdb;.pdb"}, "lib/net8.0/Uno.UI.HotDesign.dll": {"related": ".Abstractions.pdb;.Client.Core.pdb;.Client.pdb;.CommunityToolkit.pdb;.deps.json;.Hierarchy.pdb;.Messaging.pdb;.pdb;.PropertyGrid.pdb;.Toolbox.pdb;.Xaml.Interactions.pdb;.Xaml.Interactivity.pdb;.Xaml.pdb"}}, "runtime": {"lib/net8.0/Uno.UI.HotDesign.Abstractions.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Client.Core.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Client.dll": {"related": ".Core.pdb;.pdb"}, "lib/net8.0/Uno.UI.HotDesign.CommunityToolkit.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Hierarchy.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Messaging.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.PropertyGrid.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Toolbox.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactions.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactivity.dll": {"related": ".pdb"}, "lib/net8.0/Uno.UI.HotDesign.Xaml.dll": {"related": ".Interactions.pdb;.Interactivity.pdb;.pdb"}, "lib/net8.0/Uno.UI.HotDesign.dll": {"related": ".Abstractions.pdb;.Client.Core.pdb;.Client.pdb;.CommunityToolkit.pdb;.deps.json;.Hierarchy.pdb;.Messaging.pdb;.pdb;.PropertyGrid.pdb;.Toolbox.pdb;.Xaml.Interactions.pdb;.Xaml.Interactivity.pdb;.Xaml.pdb"}}, "build": {"build/Uno.UI.HotDesign.props": {}}}, "Uno.Wasm.WebSockets/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Uno.Wasm.WebSockets.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Uno.Wasm.WebSockets.dll": {"related": ".pdb"}}}, "Uno.WinRT/6.0.797": {"type": "package", "dependencies": {"Uno.Foundation": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Dispatching.dll": {}, "lib/net9.0/Uno.dll": {}}, "runtime": {"lib/net9.0/Uno.UI.Dispatching.dll": {}, "lib/net9.0/Uno.dll": {}}, "build": {"buildTransitive/Uno.WinRT.props": {}}}, "Uno.WinUI/6.0.797": {"type": "package", "dependencies": {"Uno.Diagnostics.Eventing": "2.0.1", "Uno.Fonts.Fluent": "2.6.1", "Uno.Foundation.Logging": "6.0.797", "Uno.WinRT": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Composition.dll": {}, "lib/net9.0/Uno.UI.FluentTheme.dll": {"related": ".pdb;.v1.pdb;.v2.pdb"}, "lib/net9.0/Uno.UI.FluentTheme.v1.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.FluentTheme.v2.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.Toolkit.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.dll": {"related": ".FluentTheme.pdb;.FluentTheme.v1.pdb;.FluentTheme.v2.pdb;.Toolkit.pdb"}, "lib/net9.0/Uno.Xaml.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Composition.dll": {}, "lib/net9.0/Uno.UI.FluentTheme.dll": {"related": ".pdb;.v1.pdb;.v2.pdb"}, "lib/net9.0/Uno.UI.FluentTheme.v1.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.FluentTheme.v2.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.Toolkit.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.dll": {"related": ".FluentTheme.pdb;.FluentTheme.v1.pdb;.FluentTheme.v2.pdb;.Toolkit.pdb"}, "lib/net9.0/Uno.Xaml.dll": {"related": ".pdb"}}, "build": {"buildTransitive/net8.0/uno.winui.props": {}, "buildTransitive/net8.0/uno.winui.targets": {}}}, "Uno.WinUI.DevServer/6.0.797": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.3", "Uno.Wasm.WebSockets": "1.1.0", "Uno.WinUI": "6.0.797", "Uno.WinUI.DevServer.Messaging": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.RemoteControl.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.RemoteControl.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.WinUI.DevServer.props": {}, "buildTransitive/Uno.WinUI.DevServer.targets": {}}}, "Uno.WinUI.DevServer.Messaging/6.0.797": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"lib/netstandard2.0/Uno.UI.RemoteControl.Messaging.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Uno.UI.RemoteControl.Messaging.dll": {"related": ".pdb"}}}, "Uno.WinUI.Graphics2DSK/6.0.797": {"type": "package", "dependencies": {"SkiaSharp": "3.119.0-preview.1.2", "Uno.WinUI": "6.0.797"}, "compile": {"lib/net9.0/Uno.WinUI.Graphics2DSK.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.WinUI.Graphics2DSK.dll": {"related": ".pdb"}}}, "Uno.WinUI.Lottie/6.0.797": {"type": "package", "dependencies": {"System.Json": "4.7.1", "Uno.WinUI": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Lottie.dll": {}}, "runtime": {"lib/net9.0/Uno.UI.Lottie.dll": {}}, "build": {"buildTransitive/Uno.WinUI.Lottie.targets": {}}}, "Uno.WinUI.Runtime.Skia/6.0.797": {"type": "package", "dependencies": {"Uno.WinUI": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Runtime.Skia.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.dll": {"related": ".pdb"}}}, "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer/6.0.797": {"type": "package", "dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "HarfBuzzSharp.NativeAssets.Linux": "8.3.1.1-preview.1.2", "Microsoft.Win32.Registry": "4.7.0", "SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.Linux": "3.119.0-preview.1.2", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Runtime.Skia.Linux.FrameBuffer.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.Linux.FrameBuffer.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.WinUI.Runtime.Skia.Linux.FrameBuffer.props": {}, "buildTransitive/Uno.WinUI.Runtime.Skia.Linux.FrameBuffer.targets": {}}}, "Uno.WinUI.Runtime.Skia.MacOS/6.0.797": {"type": "package", "dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "HarfBuzzSharp.NativeAssets.macOS": "8.3.1.1-preview.1.2", "SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.macos": "3.119.0-preview.1.2", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Runtime.Skia.MacOS.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.MacOS.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.WinUI.Runtime.Skia.MacOS.props": {}, "buildTransitive/Uno.WinUI.Runtime.Skia.MacOS.targets": {}}, "runtimeTargets": {"runtimes/osx/native/libUnoNativeMac.dylib": {"assetType": "native", "rid": "osx"}}}, "Uno.WinUI.Runtime.Skia.Win32/6.0.797": {"type": "package", "dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "Microsoft.Windows.CsWin32": "0.3.106", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Runtime.Skia.Win32.Support.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.Runtime.Skia.Win32.dll": {"related": ".pdb;.Support.pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.Win32.Support.dll": {"related": ".pdb"}, "lib/net9.0/Uno.UI.Runtime.Skia.Win32.dll": {"related": ".pdb;.Support.pdb"}}, "build": {"buildTransitive/Uno.WinUI.Runtime.Skia.Win32.props": {}, "buildTransitive/Uno.WinUI.Runtime.Skia.Win32.targets": {}}}, "Uno.WinUI.Runtime.Skia.Wpf/6.0.797": {"type": "package", "dependencies": {"HarfBuzzSharp": "8.3.1.1-preview.1.2", "Microsoft.Win32.SystemEvents": "9.0.0", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Runtime.Skia.Wpf.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.Wpf.dll": {"related": ".pdb"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "build": {"buildTransitive/Uno.WinUI.Runtime.Skia.Wpf.props": {}, "buildTransitive/Uno.WinUI.Runtime.Skia.Wpf.targets": {}}}, "Uno.WinUI.Runtime.Skia.X11/6.0.797": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Linux": "8.3.1.1-preview.1.2", "HarfbuzzSharp": "8.3.1.1-preview.1.2", "LibVLCSharp": "3.7.0", "SkiaSharp": "3.119.0-preview.1.2", "SkiaSharp.NativeAssets.Linux": "3.119.0-preview.1.2", "Tmds.DBus.Protocol": "0.21.2", "Uno.WinUI": "6.0.797", "Uno.WinUI.Runtime.Skia": "6.0.797"}, "compile": {"lib/net9.0/Uno.UI.Runtime.Skia.X11.dll": {"related": ".pdb"}}, "runtime": {"lib/net9.0/Uno.UI.Runtime.Skia.X11.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Uno.WinUI.Runtime.Skia.X11.props": {}, "buildTransitive/Uno.WinUI.Runtime.Skia.X11.targets": {}}}}}, "libraries": {"CommonServiceLocator/2.0.5": {"sha512": "Md2q9f6hAuUAm6IWTybQbYvxhDAs2dqmsudVDghpg0Q+C0M2KkmiTDjeGx1PsYhnZPvp+hy1Cx99v0VPW9W6/g==", "type": "package", "path": "commonservicelocator/2.0.5", "files": [".nupkg.metadata", ".signature.p7s", "commonservicelocator.2.0.5.nupkg.sha512", "commonservicelocator.nuspec", "lib/net40/CommonServiceLocator.dll", "lib/net45/CommonServiceLocator.dll", "lib/net46/CommonServiceLocator.dll", "lib/net47/CommonServiceLocator.dll", "lib/net48/CommonServiceLocator.dll", "lib/netcoreapp1.0/CommonServiceLocator.dll", "lib/netcoreapp2.0/CommonServiceLocator.dll", "lib/netcoreapp3.0/CommonServiceLocator.dll", "lib/netstandard1.0/CommonServiceLocator.dll", "lib/netstandard2.0/CommonServiceLocator.dll", "lib/netstandard2.1/CommonServiceLocator.dll"]}, "CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "HarfBuzzSharp/8.3.1.1-preview.1.2": {"sha512": "raAYK1w/dRFWmyiIW+KpDj5zhUdQDtbzTK7q6YP+qtHmbhrHS3re0MyQrfI8ypOCMnPiUItP/R5ellizN6pbHg==", "type": "package", "path": "harfbuzzsharp/8.3.1.1-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "harfbuzzsharp.8.3.1.1-preview.1.2.nupkg.sha512", "harfbuzzsharp.nuspec", "icon.png", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net8.0-android34.0/HarfBuzzSharp.dll", "lib/net8.0-android34.0/HarfBuzzSharp.pdb", "lib/net8.0-android34.0/HarfBuzzSharp.xml", "lib/net8.0-ios17.0/HarfBuzzSharp.dll", "lib/net8.0-ios17.0/HarfBuzzSharp.pdb", "lib/net8.0-maccatalyst17.0/HarfBuzzSharp.dll", "lib/net8.0-maccatalyst17.0/HarfBuzzSharp.pdb", "lib/net8.0-macos14.0/HarfBuzzSharp.dll", "lib/net8.0-macos14.0/HarfBuzzSharp.pdb", "lib/net8.0-tizen7.0/HarfBuzzSharp.dll", "lib/net8.0-tizen7.0/HarfBuzzSharp.pdb", "lib/net8.0-tvos17.0/HarfBuzzSharp.dll", "lib/net8.0-tvos17.0/HarfBuzzSharp.pdb", "lib/net8.0-windows10.0.19041/HarfBuzzSharp.dll", "lib/net8.0-windows10.0.19041/HarfBuzzSharp.pdb", "lib/net8.0/HarfBuzzSharp.dll", "lib/net8.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb"]}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1-preview.1.2": {"sha512": "zftAUE5axEqe/F7/s9/h/SAw6Bt+Bu1htv7Fk1NvZNfx0l/5vxnGwG/lFcfKA1pFo8sMffTtTLaRo8eVEM34Ag==", "type": "package", "path": "harfbuzzsharp.nativeassets.linux/8.3.1.1-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "harfbuzzsharp.nativeassets.linux.8.3.1.1-preview.1.2.nupkg.sha512", "harfbuzzsharp.nativeassets.linux.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/linux-arm/native/libHarfBuzzSharp.so", "runtimes/linux-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-loongarch64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-arm/native/libHarfBuzzSharp.so", "runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "runtimes/linux-riscv64/native/libHarfBuzzSharp.so", "runtimes/linux-x64/native/libHarfBuzzSharp.so", "runtimes/linux-x86/native/libHarfBuzzSharp.so"]}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1-preview.1.2": {"sha512": "zOV4y9jvG5AIAiFTpjOdXqEum+yWoqRV88Eq008IbP/qxqG+UwUCDRsYIxbiCFRGpSNWG7u1nJO54pPITT+swg==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/8.3.1.1-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net8.0-macos14.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.8.3.1.1-preview.1.2.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0-macos14.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1-preview.1.2": {"sha512": "8ZSNKExR0ewPWfR79eCID9z2j95kcrhLP+34mIkjfiD/SKq5CA84pP8TDJw5/oDxPorZ5i9DCKR+C9CaNESOBA==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/8.3.1.1-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.8.3.1.1-preview.1.2.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0-windows10.0.19041/_._", "lib/net6.0/_._", "lib/net8.0-windows10.0.19041/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "LibVLCSharp/3.7.0": {"sha512": "N9NV+BamKr+1YiRktX9JroE4m9C+pEszUNW4qa3A5mychhCWBvvoDolZKWk2yxhZvHewgaXXahBcw5ivCXjKYA==", "type": "package", "path": "libvlcsharp/3.7.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/monoandroid81/LibVLCSharp.Android.AWindow.dll", "lib/monoandroid81/LibVLCSharp.dll", "lib/monoandroid81/LibVLCSharp.xml", "lib/net40/LibVLCSharp.dll", "lib/net40/LibVLCSharp.xml", "lib/net471/LibVLCSharp.dll", "lib/net471/LibVLCSharp.xml", "lib/net6.0-android31.0/LibVLCSharp.Android.AWindow.dll", "lib/net6.0-android31.0/LibVLCSharp.dll", "lib/net6.0-android31.0/LibVLCSharp.xml", "lib/net6.0-ios16.1/LibVLCSharp.dll", "lib/net6.0-ios16.1/LibVLCSharp.xml", "lib/net6.0-macos13.0/LibVLCSharp.dll", "lib/net6.0-macos13.0/LibVLCSharp.xml", "lib/net6.0-tvos16.1/LibVLCSharp.dll", "lib/net6.0-tvos16.1/LibVLCSharp.xml", "lib/net6.0-windows10.0.17763/LibVLCSharp.dll", "lib/net6.0-windows10.0.17763/LibVLCSharp.pri", "lib/net6.0-windows10.0.17763/LibVLCSharp.xml", "lib/net6.0/LibVLCSharp.dll", "lib/net6.0/LibVLCSharp.xml", "lib/netstandard1.1/LibVLCSharp.dll", "lib/netstandard1.1/LibVLCSharp.xml", "lib/netstandard2.0/LibVLCSharp.dll", "lib/netstandard2.0/LibVLCSharp.xml", "lib/netstandard2.1/LibVLCSharp.dll", "lib/netstandard2.1/LibVLCSharp.xml", "lib/uap10.0.18362/LibVLCSharp.dll", "lib/uap10.0.18362/LibVLCSharp.pri", "lib/uap10.0.18362/LibVLCSharp.xml", "lib/uap10.0.18362/LibVLCSharp/LibVLCSharp.xr.xml", "lib/xamarinios10/LibVLCSharp.dll", "lib/xamarinios10/LibVLCSharp.xml", "lib/xamarinmac20/LibVLCSharp.dll", "lib/xamarinmac20/LibVLCSharp.xml", "libvlcsharp.3.7.0.nupkg.sha512", "libvlcsharp.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.6": {"sha512": "VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "type": "package", "path": "microsoft.extensions.configuration/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"sha512": "3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"sha512": "Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"sha512": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "type": "package", "path": "microsoft.extensions.configuration.commandline/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"sha512": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"sha512": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"sha512": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"sha512": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"sha512": "vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"sha512": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/8.0.0": {"sha512": "3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "type": "package", "path": "microsoft.extensions.diagnostics/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"sha512": "elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/8.0.0": {"sha512": "ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "type": "package", "path": "microsoft.extensions.hosting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net6.0/Microsoft.Extensions.Hosting.dll", "lib/net6.0/Microsoft.Extensions.Hosting.xml", "lib/net7.0/Microsoft.Extensions.Hosting.dll", "lib/net7.0/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"sha512": "nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/8.0.0": {"sha512": "cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "type": "package", "path": "microsoft.extensions.http/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Http.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Http.targets", "lib/net462/Microsoft.Extensions.Http.dll", "lib/net462/Microsoft.Extensions.Http.xml", "lib/net6.0/Microsoft.Extensions.Http.dll", "lib/net6.0/Microsoft.Extensions.Http.xml", "lib/net7.0/Microsoft.Extensions.Http.dll", "lib/net7.0/Microsoft.Extensions.Http.xml", "lib/net8.0/Microsoft.Extensions.Http.dll", "lib/net8.0/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.8.0.0.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Localization.Abstractions/8.0.1": {"sha512": "6I7L2lNIziR93fjxecmeXi/dTnsGc7zHp+zXIywA01wzy769o11Ba6IDmese3MLXYBohiWf5X/dhT04AeouSHA==", "type": "package", "path": "microsoft.extensions.localization.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net462/Microsoft.Extensions.Localization.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/9.0.6": {"sha512": "XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "type": "package", "path": "microsoft.extensions.logging/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.6.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"sha512": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"sha512": "lCgpxE5r6v43SB40/yUVnSWZUUqUZF5iUWizhkx4gqvq0L0rMw5g8adWKGO7sfIaSbCiU0et85sDQWswhLcceg==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/9.0.6": {"sha512": "L1O0M3MrqGlkrPYMLzcCphQpCG0lSHfTSPrm1otALNBzTPiO8rxxkjhBIIa2onKv92UP30Y4QaiigVMTx8YcxQ==", "type": "package", "path": "microsoft.extensions.logging.console/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/net9.0/Microsoft.Extensions.Logging.Console.dll", "lib/net9.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.9.0.6.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"sha512": "dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "type": "package", "path": "microsoft.extensions.logging.debug/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net6.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net6.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net7.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net7.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"sha512": "3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "type": "package", "path": "microsoft.extensions.logging.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"sha512": "oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "type": "package", "path": "microsoft.extensions.logging.eventsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.6": {"sha512": "wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "type": "package", "path": "microsoft.extensions.options/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.6.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"sha512": "2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.6": {"sha512": "BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "type": "package", "path": "microsoft.extensions.primitives/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.6.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Kiota.Abstractions/1.16.4": {"sha512": "pLQcH9tYRPA3CCsrqFUajUUaR8UmsqPmQExigpOfdaFvUXuMauUcCqxVnRGVnN002HqtOdsc/zKVewj6tFD6Dg==", "type": "package", "path": "microsoft.kiota.abstractions/1.16.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Microsoft.Kiota.Abstractions.dll", "lib/net5.0/Microsoft.Kiota.Abstractions.xml", "lib/net6.0/Microsoft.Kiota.Abstractions.dll", "lib/net6.0/Microsoft.Kiota.Abstractions.xml", "lib/net8.0/Microsoft.Kiota.Abstractions.dll", "lib/net8.0/Microsoft.Kiota.Abstractions.xml", "lib/netstandard2.0/Microsoft.Kiota.Abstractions.dll", "lib/netstandard2.0/Microsoft.Kiota.Abstractions.xml", "lib/netstandard2.1/Microsoft.Kiota.Abstractions.dll", "lib/netstandard2.1/Microsoft.Kiota.Abstractions.xml", "microsoft.kiota.abstractions.1.16.4.nupkg.sha512", "microsoft.kiota.abstractions.nuspec"]}, "Microsoft.Kiota.Http.HttpClientLibrary/1.16.4": {"sha512": "5qf2xS5gXgO842V/tTiNqEt5VaMENWm23KMznl8SUl+UWkBeN55KKY0abRrQrw56igkwJJG6ReBwZoPQtwqHDg==", "type": "package", "path": "microsoft.kiota.http.httpclientlibrary/1.16.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Kiota.Http.HttpClientLibrary.dll", "lib/net462/Microsoft.Kiota.Http.HttpClientLibrary.xml", "lib/net5.0/Microsoft.Kiota.Http.HttpClientLibrary.dll", "lib/net5.0/Microsoft.Kiota.Http.HttpClientLibrary.xml", "lib/net6.0/Microsoft.Kiota.Http.HttpClientLibrary.dll", "lib/net6.0/Microsoft.Kiota.Http.HttpClientLibrary.xml", "lib/net8.0-browser1.0/Microsoft.Kiota.Http.HttpClientLibrary.dll", "lib/net8.0-browser1.0/Microsoft.Kiota.Http.HttpClientLibrary.xml", "lib/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.dll", "lib/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.xml", "lib/netstandard2.0/Microsoft.Kiota.Http.HttpClientLibrary.dll", "lib/netstandard2.0/Microsoft.Kiota.Http.HttpClientLibrary.xml", "lib/netstandard2.1/Microsoft.Kiota.Http.HttpClientLibrary.dll", "lib/netstandard2.1/Microsoft.Kiota.Http.HttpClientLibrary.xml", "microsoft.kiota.http.httpclientlibrary.1.16.4.nupkg.sha512", "microsoft.kiota.http.httpclientlibrary.nuspec"]}, "Microsoft.Kiota.Serialization.Form/1.16.4": {"sha512": "P/fnIzYON3qArN3a3dFtSXKxG2hf1K8Iq8RLesQIc0naMwFDYq4SSE3JcRNJ9YQQUOHKcoeRdP29BpkCFkbrhg==", "type": "package", "path": "microsoft.kiota.serialization.form/1.16.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Microsoft.Kiota.Serialization.Form.dll", "lib/net5.0/Microsoft.Kiota.Serialization.Form.xml", "lib/net6.0/Microsoft.Kiota.Serialization.Form.dll", "lib/net6.0/Microsoft.Kiota.Serialization.Form.xml", "lib/net8.0/Microsoft.Kiota.Serialization.Form.dll", "lib/net8.0/Microsoft.Kiota.Serialization.Form.xml", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Form.dll", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Form.xml", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Form.dll", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Form.xml", "microsoft.kiota.serialization.form.1.16.4.nupkg.sha512", "microsoft.kiota.serialization.form.nuspec"]}, "Microsoft.Kiota.Serialization.Json/1.16.4": {"sha512": "WXRv4dhjwDcky+27MX6Q4L96s38ChhPSqdgOENhDsEHAZiN/YYjq1IBvQk31fnxJraqyJR/IbG84c51B1gNhJw==", "type": "package", "path": "microsoft.kiota.serialization.json/1.16.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Microsoft.Kiota.Serialization.Json.dll", "lib/net5.0/Microsoft.Kiota.Serialization.Json.xml", "lib/net6.0/Microsoft.Kiota.Serialization.Json.dll", "lib/net6.0/Microsoft.Kiota.Serialization.Json.xml", "lib/net8.0/Microsoft.Kiota.Serialization.Json.dll", "lib/net8.0/Microsoft.Kiota.Serialization.Json.xml", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Json.dll", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Json.xml", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Json.dll", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Json.xml", "microsoft.kiota.serialization.json.1.16.4.nupkg.sha512", "microsoft.kiota.serialization.json.nuspec"]}, "Microsoft.Kiota.Serialization.Multipart/1.16.4": {"sha512": "QgY2Ckeipd53H5dyPv9v8far2BAPMa4dVNnLFd/m3uS7nQWi9ammB3Aj/cBArIeUvG1bznahCf9Tx2bDdtPGhQ==", "type": "package", "path": "microsoft.kiota.serialization.multipart/1.16.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Microsoft.Kiota.Serialization.Multipart.dll", "lib/net5.0/Microsoft.Kiota.Serialization.Multipart.xml", "lib/net6.0/Microsoft.Kiota.Serialization.Multipart.dll", "lib/net6.0/Microsoft.Kiota.Serialization.Multipart.xml", "lib/net8.0/Microsoft.Kiota.Serialization.Multipart.dll", "lib/net8.0/Microsoft.Kiota.Serialization.Multipart.xml", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Multipart.dll", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Multipart.xml", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Multipart.dll", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Multipart.xml", "microsoft.kiota.serialization.multipart.1.16.4.nupkg.sha512", "microsoft.kiota.serialization.multipart.nuspec"]}, "Microsoft.Kiota.Serialization.Text/1.16.4": {"sha512": "TkThf7kvYwcGLW9OlxCkfppjJzV/YOrUfZhK8YYKshPwfOrsw4AkH35oUx/seC9pxxzb9xKecMHJ2GstunD53Q==", "type": "package", "path": "microsoft.kiota.serialization.text/1.16.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Microsoft.Kiota.Serialization.Text.dll", "lib/net5.0/Microsoft.Kiota.Serialization.Text.xml", "lib/net6.0/Microsoft.Kiota.Serialization.Text.dll", "lib/net6.0/Microsoft.Kiota.Serialization.Text.xml", "lib/net8.0/Microsoft.Kiota.Serialization.Text.dll", "lib/net8.0/Microsoft.Kiota.Serialization.Text.xml", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Text.dll", "lib/netstandard2.0/Microsoft.Kiota.Serialization.Text.xml", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Text.dll", "lib/netstandard2.1/Microsoft.Kiota.Serialization.Text.xml", "microsoft.kiota.serialization.text.1.16.4.nupkg.sha512", "microsoft.kiota.serialization.text.nuspec"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/9.0.0": {"sha512": "z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "type": "package", "path": "microsoft.win32.systemevents/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/net9.0/Microsoft.Win32.SystemEvents.dll", "lib/net9.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.9.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Windows.CsWin32/0.3.106": {"sha512": "Mx5fK7uN6fwLR4wUghs6//HonAnwPBNmC2oonyJVhCUlHS/r6SUS3NkBc3+gaQiv+0/9bqdj1oSCKQFkNI+21Q==", "type": "package", "path": "microsoft.windows.cswin32/0.3.106", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "NOTICE.txt", "README.md", "analyzers/cs/MessagePack.Annotations.dll", "analyzers/cs/MessagePack.dll", "analyzers/cs/Microsoft.Bcl.AsyncInterfaces.dll", "analyzers/cs/Microsoft.Windows.CsWin32.dll", "analyzers/cs/Microsoft.Windows.SDK.Win32Docs.dll", "analyzers/cs/System.Text.Encodings.Web.dll", "analyzers/cs/System.Text.Json.dll", "build/Microsoft.Windows.CsWin32.props", "build/net20/Microsoft.Windows.CsWin32.props", "build/netstandard1.0/Microsoft.Windows.CsWin32.props", "microsoft.windows.cswin32.0.3.106.nupkg.sha512", "microsoft.windows.cswin32.nuspec", "readme.txt", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.Windows.SDK.Win32Docs/0.1.42-alpha": {"sha512": "Z/9po23gUA9aoukirh2ItMU2ZS9++Js9Gdds9fu5yuMojDrmArvY2y+tq9985tR3cxFxpZO1O35Wjfo0khj5HA==", "type": "package", "path": "microsoft.windows.sdk.win32docs/0.1.42-alpha", "files": [".nupkg.metadata", ".signature.p7s", "apidocs.msgpack", "buildTransitive/Microsoft.Windows.SDK.Win32Docs.props", "buildTransitive/net20/Microsoft.Windows.SDK.Win32Docs.props", "buildTransitive/netstandard1.0/Microsoft.Windows.SDK.Win32Docs.props", "images/windows.png", "lib/netstandard2.0/Microsoft.Windows.SDK.Win32Docs.dll", "lib/netstandard2.0/Microsoft.Windows.SDK.Win32Docs.xml", "microsoft.windows.sdk.win32docs.0.1.42-alpha.nupkg.sha512", "microsoft.windows.sdk.win32docs.nuspec"]}, "Microsoft.Windows.SDK.Win32Metadata/60.0.34-preview": {"sha512": "TA3DUNi4CTeo+ItTXBnGZFt2159XOGSl0UOlG5vjDj4WHqZjhwYyyUnzOtrbCERiSaP2Hzg7otJNWwOSZgutyA==", "type": "package", "path": "microsoft.windows.sdk.win32metadata/60.0.34-preview", "files": [".nupkg.metadata", ".signature.p7s", "Windows.Win32.winmd", "build/Microsoft.Windows.SDK.Win32Metadata.props", "build/net20/Microsoft.Windows.SDK.Win32Metadata.props", "build/netstandard1.0/Microsoft.Windows.SDK.Win32Metadata.props", "buildTransitive/Microsoft.Windows.SDK.Win32Metadata.props", "buildTransitive/net20/Microsoft.Windows.SDK.Win32Metadata.props", "buildTransitive/netstandard1.0/Microsoft.Windows.SDK.Win32Metadata.props", "images/windows.png", "microsoft.windows.sdk.win32metadata.60.0.34-preview.nupkg.sha512", "microsoft.windows.sdk.win32metadata.nuspec", "sdk_license.txt"]}, "Microsoft.Windows.WDK.Win32Metadata/0.11.4-experimental": {"sha512": "bf5MCmUyZf0gBlYQjx9UpRAZWBkRndyt9XicR+UNLvAUAFTZQbu6YaX/sNKZlR98Grn0gydfh/yT4I3vc0AIQA==", "type": "package", "path": "microsoft.windows.wdk.win32metadata/0.11.4-experimental", "files": [".nupkg.metadata", ".signature.p7s", "Windows.Wdk.winmd", "build/Microsoft.Windows.WDK.Win32Metadata.props", "build/net20/Microsoft.Windows.WDK.Win32Metadata.props", "build/netstandard1.0/Microsoft.Windows.WDK.Win32Metadata.props", "buildTransitive/Microsoft.Windows.WDK.Win32Metadata.props", "buildTransitive/net20/Microsoft.Windows.WDK.Win32Metadata.props", "buildTransitive/netstandard1.0/Microsoft.Windows.WDK.Win32Metadata.props", "images/windows.png", "microsoft.windows.wdk.win32metadata.0.11.4-experimental.nupkg.sha512", "microsoft.windows.wdk.win32metadata.nuspec", "sdk_license.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Refit/7.2.22": {"sha512": "xzpjDvWTKaJkhRejrHI6E2WXs2CaJYWZTMICq5Jha4wlSVvR3dd9n3M4p3X7BQ6uw0LJSFiHlTYwzjQIbJdArg==", "type": "package", "path": "refit/7.2.22", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "README.md", "analyzers/dotnet/roslyn3.8/cs/InterfaceStubGeneratorV1.dll", "analyzers/dotnet/roslyn4.0/cs/InterfaceStubGeneratorV2.dll", "build/netstandard2.0/refit.targets", "buildTransitive/netstandard2.0/refit.props", "buildTransitive/netstandard2.0/refit.targets", "lib/net462/Refit.dll", "lib/net462/Refit.xml", "lib/net6.0/Refit.dll", "lib/net6.0/Refit.xml", "lib/net8.0/Refit.dll", "lib/net8.0/Refit.xml", "lib/netstandard2.0/Refit.dll", "lib/netstandard2.0/Refit.xml", "refit.7.2.22.nupkg.sha512", "refit.nuspec", "refit_logo.png"]}, "SkiaSharp/3.119.0-preview.1.2": {"sha512": "YpwE8w0XAwMmik/WPpGFNH3/qnK10KkN0k+UMKsOzTzuqzv00UBsJd8/y4HEj6hXvOM6nX3LurRdq3ahluliZw==", "type": "package", "path": "skiasharp/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.dll", "lib/net8.0-android34.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.xml", "lib/net8.0-ios17.0/SkiaSharp.dll", "lib/net8.0-ios17.0/SkiaSharp.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.pdb", "lib/net8.0-macos14.0/SkiaSharp.dll", "lib/net8.0-macos14.0/SkiaSharp.pdb", "lib/net8.0-tizen7.0/SkiaSharp.dll", "lib/net8.0-tizen7.0/SkiaSharp.pdb", "lib/net8.0-tvos17.0/SkiaSharp.dll", "lib/net8.0-tvos17.0/SkiaSharp.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.pdb", "lib/net8.0/SkiaSharp.dll", "lib/net8.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "ref/net462/SkiaSharp.dll", "ref/net6.0/SkiaSharp.dll", "ref/net8.0-android34.0/SkiaSharp.dll", "ref/net8.0-ios17.0/SkiaSharp.dll", "ref/net8.0-maccatalyst17.0/SkiaSharp.dll", "ref/net8.0-macos14.0/SkiaSharp.dll", "ref/net8.0-tizen7.0/SkiaSharp.dll", "ref/net8.0-tvos17.0/SkiaSharp.dll", "ref/net8.0-windows10.0.19041/SkiaSharp.dll", "ref/net8.0/SkiaSharp.dll", "ref/netstandard2.0/SkiaSharp.dll", "ref/netstandard2.1/SkiaSharp.dll", "skiasharp.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.Linux/3.119.0-preview.1.2": {"sha512": "sIosdSfGsNovt2JVQ0B+XSkENbhAQtS9XWcetEgcwvsB1cayjpGbiD3z7M+K7hLejfhnjTTILmCEoFTkT7cXNQ==", "type": "package", "path": "skiasharp.nativeassets.linux/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Linux.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Linux.targets", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/linux-arm/native/libSkiaSharp.so", "runtimes/linux-arm64/native/libSkiaSharp.so", "runtimes/linux-loongarch64/native/libSkiaSharp.so", "runtimes/linux-musl-arm/native/libSkiaSharp.so", "runtimes/linux-musl-arm64/native/libSkiaSharp.so", "runtimes/linux-musl-loongarch64/native/libSkiaSharp.so", "runtimes/linux-musl-riscv64/native/libSkiaSharp.so", "runtimes/linux-musl-x64/native/libSkiaSharp.so", "runtimes/linux-riscv64/native/libSkiaSharp.so", "runtimes/linux-x64/native/libSkiaSharp.so", "runtimes/linux-x86/native/libSkiaSharp.so", "skiasharp.nativeassets.linux.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.nativeassets.linux.nuspec"]}, "SkiaSharp.NativeAssets.macOS/3.119.0-preview.1.2": {"sha512": "9TbuZloRaFQt8lgaDIBs5CQrDf6h9ouLrYEetFAUiGQZBrJho5wxr/WjlLGAprubOJChxtLrFSe1adp9mT5C8g==", "type": "package", "path": "skiasharp.nativeassets.macos/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net8.0-macos14.0/SkiaSharp.NativeAssets.macOS.targets", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0-macos14.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.WebAssembly/3.119.0-preview.1.2": {"sha512": "3kmbq9blotAvaOxptEryB0Zx+N5iaJqigumTJyEvPSlt/oRAh2YaEEJbKa/1nqOU8Yg3p+qs9aMphZyZdJFTSg==", "type": "package", "path": "skiasharp.nativeassets.webassembly/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "buildTransitive/netstandard1.0/libSkiaSharp.a/2.0.23/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/2.0.6/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.12/mt,simd/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.12/mt/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.12/st,simd/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.12/st/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.34/mt,simd/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.34/mt/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.34/st,simd/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.34/st/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.56/mt,simd/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.56/mt/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.56/st,simd/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.56/st/libSkiaSharp.a", "buildTransitive/netstandard1.0/libSkiaSharp.a/3.1.7/libSkiaSharp.a", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "skiasharp.nativeassets.webassembly.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.nativeassets.webassembly.nuspec"]}, "SkiaSharp.NativeAssets.Win32/3.119.0-preview.1.2": {"sha512": "j8myDNfWRQG/qzKbCuIVfmMFjmTA9zGXB0XC9T1/LVVeF7mGC5ljo77vSqtEVHtIZOEiy3MZOjCRbMZCRLkAwA==", "type": "package", "path": "skiasharp.nativeassets.win32/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "icon.png", "lib/net462/_._", "lib/net6.0-windows10.0.19041/_._", "lib/net6.0/_._", "lib/net8.0-windows10.0.19041/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "SkiaSharp.Resources/3.119.0-preview.1.2": {"sha512": "VFsQ9plcE+o2nkXUBNsQyS203+aU98MxNqiV9XtepxwGN6ifYg1zapZM4XTYVWLr+vjGDSpuz88nRqGADp+aUg==", "type": "package", "path": "skiasharp.resources/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net462/SkiaSharp.Resources.dll", "lib/net462/SkiaSharp.Resources.pdb", "lib/net6.0/SkiaSharp.Resources.dll", "lib/net6.0/SkiaSharp.Resources.pdb", "lib/net8.0-android34.0/SkiaSharp.Resources.dll", "lib/net8.0-android34.0/SkiaSharp.Resources.pdb", "lib/net8.0-android34.0/SkiaSharp.Resources.xml", "lib/net8.0-ios17.0/SkiaSharp.Resources.dll", "lib/net8.0-ios17.0/SkiaSharp.Resources.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.Resources.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.Resources.pdb", "lib/net8.0-macos14.0/SkiaSharp.Resources.dll", "lib/net8.0-macos14.0/SkiaSharp.Resources.pdb", "lib/net8.0-tizen7.0/SkiaSharp.Resources.dll", "lib/net8.0-tizen7.0/SkiaSharp.Resources.pdb", "lib/net8.0-tvos17.0/SkiaSharp.Resources.dll", "lib/net8.0-tvos17.0/SkiaSharp.Resources.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.Resources.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.Resources.pdb", "lib/net8.0/SkiaSharp.Resources.dll", "lib/net8.0/SkiaSharp.Resources.pdb", "lib/netstandard2.0/SkiaSharp.Resources.dll", "lib/netstandard2.0/SkiaSharp.Resources.pdb", "lib/netstandard2.1/SkiaSharp.Resources.dll", "lib/netstandard2.1/SkiaSharp.Resources.pdb", "skiasharp.resources.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.resources.nuspec"]}, "SkiaSharp.SceneGraph/3.119.0-preview.1.2": {"sha512": "AGlVuq1L/1+xlTpWJ9paIzcqrSOhvEHuk/KlHqLPd2EaB/Ub/upHvrx3BtExMbKf4D4Fda0+UgOzlSl8UIg8cA==", "type": "package", "path": "skiasharp.scenegraph/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net462/SkiaSharp.SceneGraph.dll", "lib/net462/SkiaSharp.SceneGraph.pdb", "lib/net6.0/SkiaSharp.SceneGraph.dll", "lib/net6.0/SkiaSharp.SceneGraph.pdb", "lib/net8.0-android34.0/SkiaSharp.SceneGraph.dll", "lib/net8.0-android34.0/SkiaSharp.SceneGraph.pdb", "lib/net8.0-android34.0/SkiaSharp.SceneGraph.xml", "lib/net8.0-ios17.0/SkiaSharp.SceneGraph.dll", "lib/net8.0-ios17.0/SkiaSharp.SceneGraph.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.SceneGraph.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.SceneGraph.pdb", "lib/net8.0-macos14.0/SkiaSharp.SceneGraph.dll", "lib/net8.0-macos14.0/SkiaSharp.SceneGraph.pdb", "lib/net8.0-tizen7.0/SkiaSharp.SceneGraph.dll", "lib/net8.0-tizen7.0/SkiaSharp.SceneGraph.pdb", "lib/net8.0-tvos17.0/SkiaSharp.SceneGraph.dll", "lib/net8.0-tvos17.0/SkiaSharp.SceneGraph.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.SceneGraph.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.SceneGraph.pdb", "lib/net8.0/SkiaSharp.SceneGraph.dll", "lib/net8.0/SkiaSharp.SceneGraph.pdb", "lib/netstandard2.0/SkiaSharp.SceneGraph.dll", "lib/netstandard2.0/SkiaSharp.SceneGraph.pdb", "lib/netstandard2.1/SkiaSharp.SceneGraph.dll", "lib/netstandard2.1/SkiaSharp.SceneGraph.pdb", "skiasharp.scenegraph.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.scenegraph.nuspec"]}, "SkiaSharp.Skottie/3.119.0-preview.1.2": {"sha512": "1sAxgz+kOUUxTYZZc9ouWbcyeV4IspkiWnKr3353S/XSn/U4eUILN+J0tK4zCb3S6lP8ie6XlQiAGzoAaOIoSA==", "type": "package", "path": "skiasharp.skottie/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net462/SkiaSharp.Skottie.dll", "lib/net462/SkiaSharp.Skottie.pdb", "lib/net6.0/SkiaSharp.Skottie.dll", "lib/net6.0/SkiaSharp.Skottie.pdb", "lib/net8.0-android34.0/SkiaSharp.Skottie.dll", "lib/net8.0-android34.0/SkiaSharp.Skottie.pdb", "lib/net8.0-android34.0/SkiaSharp.Skottie.xml", "lib/net8.0-ios17.0/SkiaSharp.Skottie.dll", "lib/net8.0-ios17.0/SkiaSharp.Skottie.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.Skottie.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.Skottie.pdb", "lib/net8.0-macos14.0/SkiaSharp.Skottie.dll", "lib/net8.0-macos14.0/SkiaSharp.Skottie.pdb", "lib/net8.0-tizen7.0/SkiaSharp.Skottie.dll", "lib/net8.0-tizen7.0/SkiaSharp.Skottie.pdb", "lib/net8.0-tvos17.0/SkiaSharp.Skottie.dll", "lib/net8.0-tvos17.0/SkiaSharp.Skottie.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.Skottie.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.Skottie.pdb", "lib/net8.0/SkiaSharp.Skottie.dll", "lib/net8.0/SkiaSharp.Skottie.pdb", "lib/netstandard2.0/SkiaSharp.Skottie.dll", "lib/netstandard2.0/SkiaSharp.Skottie.pdb", "lib/netstandard2.1/SkiaSharp.Skottie.dll", "lib/netstandard2.1/SkiaSharp.Skottie.pdb", "skiasharp.skottie.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.skottie.nuspec"]}, "SkiaSharp.Views.Uno.WinUI/3.119.0-preview.1.2": {"sha512": "sHOovewGIxD408XO7rJaxrFdhYYv3gOGs9AtIpqu3vTciY0AvYlt7rdw9kn4HvgpedJRvvQc9YhdwUeER0C6UQ==", "type": "package", "path": "skiasharp.views.uno.winui/3.119.0-preview.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "buildTransitive/net8.0/SkiaSharp.Views.Uno.WinUI.targets", "icon.png", "lib/net6.0-windows10.0.19041.0/_._", "lib/net8.0-android34.0/SkiaSharp.Views.Windows.aar", "lib/net8.0-android34.0/SkiaSharp.Views.Windows.dll", "lib/net8.0-android34.0/SkiaSharp.Views.Windows.pdb", "lib/net8.0-android34.0/SkiaSharp.Views.Windows.xml", "lib/net8.0-ios17.0/SkiaSharp.Views.Windows.dll", "lib/net8.0-ios17.0/SkiaSharp.Views.Windows.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.Views.Windows.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.Views.Windows.pdb", "lib/net8.0-macos14.0/SkiaSharp.Views.Windows.dll", "lib/net8.0-macos14.0/SkiaSharp.Views.Windows.pdb", "lib/net8.0-windows10.0.19041.0/_._", "lib/net8.0/SkiaSharp.Views.Windows.dll", "lib/net8.0/SkiaSharp.Views.Windows.pdb", "skiasharp.views.uno.winui.3.119.0-preview.1.2.nupkg.sha512", "skiasharp.views.uno.winui.nuspec", "uno-runtime/net8.0/skia/SkiaSharp.Views.Windows.dll", "uno-runtime/net8.0/skia/SkiaSharp.Views.Windows.pdb", "uno-runtime/net8.0/skia/SkiaSharp.Views.Windows.xml", "uno-runtime/net8.0/webassembly/SkiaSharp.Views.Windows.dll", "uno-runtime/net8.0/webassembly/SkiaSharp.Views.Windows.pdb", "uno-runtime/net8.0/webassembly/SkiaSharp.Views.Windows.xml"]}, "Std.UriTemplate/2.0.1": {"sha512": "Ix5VXZwLfolwVHyGTSSJl6KIJ2le6E9YjLdZBMS1Xxzw7VJankRvQW8JoUL69tEgfcw+0qjgWrlxANrhvS0QCQ==", "type": "package", "path": "std.uritemplate/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Readme.md", "lib/net5.0/Std.UriTemplate.dll", "lib/netstandard2.0/Std.UriTemplate.dll", "std.uritemplate.2.0.1.nupkg.sha512", "std.uritemplate.nuspec"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/8.0.0": {"sha512": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "type": "package", "path": "system.diagnostics.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Json/4.7.1": {"sha512": "XymImdgljgVEkd9trFSKWlNJac1Hr+L9lwFLQSUUQfFMkm88AHI6DINAMhpoiUWQIPLIqEjR702eydUb4Qb3Ng==", "type": "package", "path": "system.json/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/Xamarinmac20/_._", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net461/System.Json.dll", "lib/net461/System.Json.xml", "lib/netstandard1.0/System.Json.dll", "lib/netstandard1.0/System.Json.xml", "lib/netstandard2.0/System.Json.dll", "lib/netstandard2.0/System.Json.xml", "lib/xamarinios10/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/Xamarinmac20/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/xamarinios10/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.json.4.7.1.nupkg.sha512", "system.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Linq.Async/4.0.0": {"sha512": "WbiYEedFZeM+psmMyoCt1AKbZppAZg8Eq1ZTQ+521fGNeXqlgJj0tZYV5n1LsKRO5osQuitYxGNuzPTy3213sg==", "type": "package", "path": "system.linq.async/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.Linq.Async.dll", "lib/net461/System.Linq.Async.xml", "lib/netcoreapp3.0/System.Linq.Async.dll", "lib/netcoreapp3.0/System.Linq.Async.xml", "lib/netstandard2.0/System.Linq.Async.dll", "lib/netstandard2.0/System.Linq.Async.xml", "lib/netstandard2.1/System.Linq.Async.dll", "lib/netstandard2.1/System.Linq.Async.xml", "ref/net461/System.Linq.Async.dll", "ref/net461/System.Linq.Async.xml", "ref/netcoreapp3.0/System.Linq.Async.dll", "ref/netcoreapp3.0/System.Linq.Async.xml", "ref/netstandard2.0/System.Linq.Async.dll", "ref/netstandard2.0/System.Linq.Async.xml", "ref/netstandard2.1/System.Linq.Async.dll", "ref/netstandard2.1/System.Linq.Async.xml", "system.linq.async.4.0.0.nupkg.sha512", "system.linq.async.nuspec"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Tmds.DBus.Protocol/0.21.2": {"sha512": "ScSMrUrrw8px4kK1Glh0fZv/HQUlg1078bNXNPfRPKQ3WbRzV9HpsydYEOgSoMK5LWICMf2bMwIFH0pGjxjcMA==", "type": "package", "path": "tmds.dbus.protocol/0.21.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Tmds.DBus.Protocol.dll", "lib/net8.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.1/Tmds.DBus.Protocol.dll", "tmds.dbus.protocol.0.21.2.nupkg.sha512", "tmds.dbus.protocol.nuspec"]}, "Uno.Core.Extensions/4.1.1": {"sha512": "2IFxilOCmozAJ+zLHZAONWjOBi3i8Nr5ndAsTjbB6enG9NkrYC/lv8/GF6c5Sj3e0TmRSLXWjutEZGHwLxj3pg==", "type": "package", "path": "uno.core.extensions/4.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Uno.Core.Extensions.dll", "lib/net46/Uno.Core.Extensions.pdb", "lib/net46/Uno.Core.xml", "lib/net5.0/Uno.Core.Extensions.dll", "lib/net5.0/Uno.Core.Extensions.pdb", "lib/net5.0/Uno.Core.xml", "lib/net6.0/Uno.Core.Extensions.dll", "lib/net6.0/Uno.Core.Extensions.pdb", "lib/net6.0/Uno.Core.xml", "lib/net7.0/Uno.Core.Extensions.dll", "lib/net7.0/Uno.Core.Extensions.pdb", "lib/net7.0/Uno.Core.xml", "lib/netstandard2.0/Uno.Core.Extensions.dll", "lib/netstandard2.0/Uno.Core.Extensions.pdb", "lib/netstandard2.0/Uno.Core.xml", "lib/netstandard2.1/Uno.Core.Extensions.dll", "lib/netstandard2.1/Uno.Core.Extensions.pdb", "lib/netstandard2.1/Uno.Core.xml", "lib/uap10.0.17763/Uno.Core.Extensions.dll", "lib/uap10.0.17763/Uno.Core.Extensions.pdb", "lib/uap10.0.17763/Uno.Core.Extensions.pri", "lib/uap10.0.17763/Uno.Core.xml", "lib/uap10.0.19041/Uno.Core.Extensions.dll", "lib/uap10.0.19041/Uno.Core.Extensions.pdb", "lib/uap10.0.19041/Uno.Core.Extensions.pri", "lib/uap10.0.19041/Uno.Core.xml", "uno-logo.png", "uno.core.extensions.4.1.1.nupkg.sha512", "uno.core.extensions.nuspec"]}, "Uno.Core.Extensions.Collections/4.1.1": {"sha512": "ZJFsUkMQR0vL9VUiy/+QRoUmTRJkJsLbIatU5QMOTwQzHsCqTtkvm2DF31Z1UeXu10EH+1J/YXAXr0LS+nOw5A==", "type": "package", "path": "uno.core.extensions.collections/4.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Uno.Core.Extensions.Collections.dll", "lib/net46/Uno.Core.Extensions.Collections.pdb", "lib/net46/Uno.Core.xml", "lib/net5.0/Uno.Core.Extensions.Collections.dll", "lib/net5.0/Uno.Core.Extensions.Collections.pdb", "lib/net5.0/Uno.Core.xml", "lib/net6.0/Uno.Core.Extensions.Collections.dll", "lib/net6.0/Uno.Core.Extensions.Collections.pdb", "lib/net6.0/Uno.Core.xml", "lib/net7.0/Uno.Core.Extensions.Collections.dll", "lib/net7.0/Uno.Core.Extensions.Collections.pdb", "lib/net7.0/Uno.Core.xml", "lib/netstandard2.0/Uno.Core.Extensions.Collections.dll", "lib/netstandard2.0/Uno.Core.Extensions.Collections.pdb", "lib/netstandard2.0/Uno.Core.xml", "lib/netstandard2.1/Uno.Core.Extensions.Collections.dll", "lib/netstandard2.1/Uno.Core.Extensions.Collections.pdb", "lib/netstandard2.1/Uno.Core.xml", "lib/uap10.0.17763/Uno.Core.Extensions.Collections.dll", "lib/uap10.0.17763/Uno.Core.Extensions.Collections.pdb", "lib/uap10.0.17763/Uno.Core.Extensions.Collections.pri", "lib/uap10.0.17763/Uno.Core.xml", "lib/uap10.0.19041/Uno.Core.Extensions.Collections.dll", "lib/uap10.0.19041/Uno.Core.Extensions.Collections.pdb", "lib/uap10.0.19041/Uno.Core.Extensions.Collections.pri", "lib/uap10.0.19041/Uno.Core.xml", "uno-logo.png", "uno.core.extensions.collections.4.1.1.nupkg.sha512", "uno.core.extensions.collections.nuspec"]}, "Uno.Core.Extensions.Disposables/4.1.1": {"sha512": "NSsolz0P5R5lpgtBx8yf9F8qfjuw4+wykGsr99JDkYkbZ+ZkD651uW0lbvl6/lbQgcSljVgSExMnOw7WonbyVQ==", "type": "package", "path": "uno.core.extensions.disposables/4.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Uno.Core.Extensions.Disposables.dll", "lib/net46/Uno.Core.Extensions.Disposables.pdb", "lib/net46/Uno.Core.xml", "lib/net5.0/Uno.Core.Extensions.Disposables.dll", "lib/net5.0/Uno.Core.Extensions.Disposables.pdb", "lib/net5.0/Uno.Core.xml", "lib/net6.0/Uno.Core.Extensions.Disposables.dll", "lib/net6.0/Uno.Core.Extensions.Disposables.pdb", "lib/net6.0/Uno.Core.xml", "lib/net7.0/Uno.Core.Extensions.Disposables.dll", "lib/net7.0/Uno.Core.Extensions.Disposables.pdb", "lib/net7.0/Uno.Core.xml", "lib/netstandard2.0/Uno.Core.Extensions.Disposables.dll", "lib/netstandard2.0/Uno.Core.Extensions.Disposables.pdb", "lib/netstandard2.0/Uno.Core.xml", "lib/netstandard2.1/Uno.Core.Extensions.Disposables.dll", "lib/netstandard2.1/Uno.Core.Extensions.Disposables.pdb", "lib/netstandard2.1/Uno.Core.xml", "lib/uap10.0.17763/Uno.Core.Extensions.Disposables.dll", "lib/uap10.0.17763/Uno.Core.Extensions.Disposables.pdb", "lib/uap10.0.17763/Uno.Core.Extensions.Disposables.pri", "lib/uap10.0.17763/Uno.Core.xml", "lib/uap10.0.19041/Uno.Core.Extensions.Disposables.dll", "lib/uap10.0.19041/Uno.Core.Extensions.Disposables.pdb", "lib/uap10.0.19041/Uno.Core.Extensions.Disposables.pri", "lib/uap10.0.19041/Uno.Core.xml", "uno-logo.png", "uno.core.extensions.disposables.4.1.1.nupkg.sha512", "uno.core.extensions.disposables.nuspec"]}, "Uno.Core.Extensions.Equality/4.1.1": {"sha512": "BvJIN/RELgUxZadN+HO1PpvV0i3JA4sXgjlUtMin8gOr/zAoSQLeYWWhcVm+s5LqWj7oYGguqN3kw+peKUI0+w==", "type": "package", "path": "uno.core.extensions.equality/4.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Uno.Core.Extensions.Equality.dll", "lib/net46/Uno.Core.Extensions.Equality.pdb", "lib/net46/Uno.Core.xml", "lib/net5.0/Uno.Core.Extensions.Equality.dll", "lib/net5.0/Uno.Core.Extensions.Equality.pdb", "lib/net5.0/Uno.Core.xml", "lib/net6.0/Uno.Core.Extensions.Equality.dll", "lib/net6.0/Uno.Core.Extensions.Equality.pdb", "lib/net6.0/Uno.Core.xml", "lib/net7.0/Uno.Core.Extensions.Equality.dll", "lib/net7.0/Uno.Core.Extensions.Equality.pdb", "lib/net7.0/Uno.Core.xml", "lib/netstandard2.0/Uno.Core.Extensions.Equality.dll", "lib/netstandard2.0/Uno.Core.Extensions.Equality.pdb", "lib/netstandard2.0/Uno.Core.xml", "lib/netstandard2.1/Uno.Core.Extensions.Equality.dll", "lib/netstandard2.1/Uno.Core.Extensions.Equality.pdb", "lib/netstandard2.1/Uno.Core.xml", "lib/uap10.0.17763/Uno.Core.Extensions.Equality.dll", "lib/uap10.0.17763/Uno.Core.Extensions.Equality.pdb", "lib/uap10.0.17763/Uno.Core.Extensions.Equality.pri", "lib/uap10.0.17763/Uno.Core.xml", "lib/uap10.0.19041/Uno.Core.Extensions.Equality.dll", "lib/uap10.0.19041/Uno.Core.Extensions.Equality.pdb", "lib/uap10.0.19041/Uno.Core.Extensions.Equality.pri", "lib/uap10.0.19041/Uno.Core.xml", "uno-logo.png", "uno.core.extensions.equality.4.1.1.nupkg.sha512", "uno.core.extensions.equality.nuspec"]}, "Uno.Core.Extensions.Logging/4.0.1": {"sha512": "f4nnnXbdiJWKKOfo1uJnZcAl36GvTuuuyG80VKA5nfKB7UpBMU1d/lB70TcOYESSBpT17xowmwcyB8hb6+i9ZA==", "type": "package", "path": "uno.core.extensions.logging/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Uno.Core.Extensions.Logging.dll", "lib/net46/Uno.Core.Extensions.Logging.pdb", "lib/net46/Uno.Core.xml", "lib/net5.0/Uno.Core.Extensions.Logging.dll", "lib/net5.0/Uno.Core.Extensions.Logging.pdb", "lib/net5.0/Uno.Core.xml", "lib/net6.0/Uno.Core.Extensions.Logging.dll", "lib/net6.0/Uno.Core.Extensions.Logging.pdb", "lib/net6.0/Uno.Core.xml", "lib/netstandard2.0/Uno.Core.Extensions.Logging.dll", "lib/netstandard2.0/Uno.Core.Extensions.Logging.pdb", "lib/netstandard2.0/Uno.Core.xml", "lib/netstandard2.1/Uno.Core.Extensions.Logging.dll", "lib/netstandard2.1/Uno.Core.Extensions.Logging.pdb", "lib/netstandard2.1/Uno.Core.xml", "lib/uap10.0.17763/Uno.Core.Extensions.Logging.dll", "lib/uap10.0.17763/Uno.Core.Extensions.Logging.pdb", "lib/uap10.0.17763/Uno.Core.Extensions.Logging.pri", "lib/uap10.0.17763/Uno.Core.xml", "lib/uap10.0.19041/Uno.Core.Extensions.Logging.dll", "lib/uap10.0.19041/Uno.Core.Extensions.Logging.pdb", "lib/uap10.0.19041/Uno.Core.Extensions.Logging.pri", "lib/uap10.0.19041/Uno.Core.xml", "uno-logo.png", "uno.core.extensions.logging.4.0.1.nupkg.sha512", "uno.core.extensions.logging.nuspec"]}, "Uno.Core.Extensions.Logging.Singleton/4.1.1": {"sha512": "P+MQuWFpfDzKNs4UMqQp0WxBGl2MAsxUZrWbdC6rFUWs1ZLCH8B8u8nbIloTnMniPOut3YYdTQHPNLhhTUYrzg==", "type": "package", "path": "uno.core.extensions.logging.singleton/4.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Uno.Core.Extensions.Logging.Singleton.dll", "lib/net46/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/net46/Uno.Core.xml", "lib/net5.0/Uno.Core.Extensions.Logging.Singleton.dll", "lib/net5.0/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/net5.0/Uno.Core.xml", "lib/net6.0/Uno.Core.Extensions.Logging.Singleton.dll", "lib/net6.0/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/net6.0/Uno.Core.xml", "lib/net7.0/Uno.Core.Extensions.Logging.Singleton.dll", "lib/net7.0/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/net7.0/Uno.Core.xml", "lib/netstandard2.0/Uno.Core.Extensions.Logging.Singleton.dll", "lib/netstandard2.0/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/netstandard2.0/Uno.Core.xml", "lib/netstandard2.1/Uno.Core.Extensions.Logging.Singleton.dll", "lib/netstandard2.1/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/netstandard2.1/Uno.Core.xml", "lib/uap10.0.17763/Uno.Core.Extensions.Logging.Singleton.dll", "lib/uap10.0.17763/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/uap10.0.17763/Uno.Core.Extensions.Logging.Singleton.pri", "lib/uap10.0.17763/Uno.Core.xml", "lib/uap10.0.19041/Uno.Core.Extensions.Logging.Singleton.dll", "lib/uap10.0.19041/Uno.Core.Extensions.Logging.Singleton.pdb", "lib/uap10.0.19041/Uno.Core.Extensions.Logging.Singleton.pri", "lib/uap10.0.19041/Uno.Core.xml", "uno-logo.png", "uno.core.extensions.logging.singleton.4.1.1.nupkg.sha512", "uno.core.extensions.logging.singleton.nuspec"]}, "Uno.Diagnostics.Eventing/2.0.1": {"sha512": "Hd03em+Bga+w+ljCtiyc+713PQDtCB0Qkw3sBggvXXdDB2URwEc3N4TnM3vFeXpmHHZGM7dN7D8qxfiGoEEHzw==", "type": "package", "path": "uno.diagnostics.eventing/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Uno.Diagnostics.Eventing.dll", "lib/net5.0/Uno.Diagnostics.Eventing.pdb", "lib/netstandard2.0/Uno.Diagnostics.Eventing.dll", "lib/netstandard2.0/Uno.Diagnostics.Eventing.pdb", "uno.diagnostics.eventing.2.0.1.nupkg.sha512", "uno.diagnostics.eventing.nuspec"]}, "Uno.Dsp.Tasks/1.4.0": {"sha512": "Kb+S32ulgJGjprmoQW93GSu/T5emcmNtyK5Y/hJN54ovbEfK4S/NItxgJKjCRtQ2W9jJZxW8WwoxadnDTdAk5A==", "type": "package", "path": "uno.dsp.tasks/1.4.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Uno.Dsp.Tasks.targets", "tools/Microsoft.Bcl.AsyncInterfaces.dll", "tools/Microsoft.Extensions.Configuration.Abstractions.dll", "tools/Microsoft.Extensions.Configuration.Binder.dll", "tools/Microsoft.Extensions.Configuration.CommandLine.dll", "tools/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "tools/Microsoft.Extensions.Configuration.FileExtensions.dll", "tools/Microsoft.Extensions.Configuration.Json.dll", "tools/Microsoft.Extensions.Configuration.UserSecrets.dll", "tools/Microsoft.Extensions.Configuration.dll", "tools/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/Microsoft.Extensions.DependencyInjection.dll", "tools/Microsoft.Extensions.Diagnostics.Abstractions.dll", "tools/Microsoft.Extensions.Diagnostics.dll", "tools/Microsoft.Extensions.FileProviders.Abstractions.dll", "tools/Microsoft.Extensions.FileProviders.Physical.dll", "tools/Microsoft.Extensions.FileSystemGlobbing.dll", "tools/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/Microsoft.Extensions.Hosting.dll", "tools/Microsoft.Extensions.Logging.Abstractions.dll", "tools/Microsoft.Extensions.Logging.Configuration.dll", "tools/Microsoft.Extensions.Logging.Console.dll", "tools/Microsoft.Extensions.Logging.Debug.dll", "tools/Microsoft.Extensions.Logging.EventLog.dll", "tools/Microsoft.Extensions.Logging.EventSource.dll", "tools/Microsoft.Extensions.Logging.dll", "tools/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "tools/Microsoft.Extensions.Options.dll", "tools/Microsoft.Extensions.Primitives.dll", "tools/Mono.Cecil.Mdb.dll", "tools/Mono.Cecil.Pdb.dll", "tools/Mono.Cecil.Rocks.dll", "tools/Mono.Cecil.dll", "tools/System.Buffers.dll", "tools/System.Collections.Immutable.dll", "tools/System.ComponentModel.Annotations.dll", "tools/System.Diagnostics.DiagnosticSource.dll", "tools/System.Diagnostics.EventLog.dll", "tools/System.IO.Pipelines.dll", "tools/System.Memory.dll", "tools/System.Numerics.Vectors.dll", "tools/System.Runtime.CompilerServices.Unsafe.dll", "tools/System.Security.Principal.Windows.dll", "tools/System.Text.Encodings.Web.dll", "tools/System.Text.Json.dll", "tools/System.Threading.Tasks.Extensions.dll", "tools/Uno.Core.Extensions.dll", "tools/Uno.Dsp.Tasks.dll", "tools/Uno.Dsp.dll", "tools/Uno.Extensions.XamlToCsharp.dll", "uno-logo.png", "uno.dsp.tasks.1.4.0.nupkg.sha512", "uno.dsp.tasks.nuspec"]}, "Uno.Extensions.Authentication/6.0.12": {"sha512": "9I9Qkew02jE90+pCnhtUDdykE/ahrylydNgeH7OGwb7mbNaKtRc8bSAkDYFi1RhdzJUGM2pdy2qtqj69adiIvw==", "type": "package", "path": "uno.extensions.authentication/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Authentication.targets", "lib/net8.0/Uno.Extensions.Authentication.dll", "lib/net8.0/Uno.Extensions.Authentication.pdb", "lib/net8.0/Uno.Extensions.Authentication.xml", "uno.extensions.authentication.6.0.12.nupkg.sha512", "uno.extensions.authentication.nuspec", "uno.png"]}, "Uno.Extensions.Configuration/6.0.12": {"sha512": "1Ir7PF1K7Hl154qnu9iBHf7f2jWmTDctd0Nz/52cN7jwKePr/qX0eyVipTp0dg80yAqK1Vg7fKSvln9ge9GODQ==", "type": "package", "path": "uno.extensions.configuration/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Configuration.targets", "lib/net8.0/Uno.Extensions.Configuration.dll", "lib/net8.0/Uno.Extensions.Configuration.pdb", "lib/net8.0/Uno.Extensions.Configuration.xml", "uno.extensions.configuration.6.0.12.nupkg.sha512", "uno.extensions.configuration.nuspec", "uno.png"]}, "Uno.Extensions.Core/6.0.12": {"sha512": "30IfNQvj4nN7IZOKlZo/SBdwYOl2NZMvu3TcDlvvizkb8OlWuBfM7H1XMAfx+7iHakusDwofzVOYUO4xzTgWBw==", "type": "package", "path": "uno.extensions.core/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Uno.Extensions.Core.Generators.dll", "buildTransitive/Uno.Extensions.Core.props", "buildTransitive/Uno.Extensions.Core.targets", "lib/net8.0/Uno.Extensions.Core.dll", "lib/net8.0/Uno.Extensions.Core.pdb", "lib/net8.0/Uno.Extensions.Core.xml", "uno.extensions.core.6.0.12.nupkg.sha512", "uno.extensions.core.nuspec", "uno.png"]}, "Uno.Extensions.Core.WinUI/6.0.12": {"sha512": "GTyEIS0E35Y6XimWN3c0Khv/LSFIOUFdLl0RHjcxr1IXXrkL3TIF8cRzCQZe7QqsEu7U9ZVk0dMq7wrjtBkFeg==", "type": "package", "path": "uno.extensions.core.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0-android34.0/Uno.Extensions.Core.UI.aar", "lib/net8.0-android34.0/Uno.Extensions.Core.UI.dll", "lib/net8.0-android34.0/Uno.Extensions.Core.UI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Core.UI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Core.UI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Core.UI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Core.UI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Core.UI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Core.UI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Core.UI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Core.UI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Core.UI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Core.UI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Core.UI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Core.UI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Core.UI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Core.UI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Core.UI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Core.UI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Core.UI.xml", "lib/net8.0/Uno.Extensions.Core.UI.dll", "lib/net8.0/Uno.Extensions.Core.UI.pdb", "lib/net8.0/Uno.Extensions.Core.UI.xml", "uno.extensions.core.winui.6.0.12.nupkg.sha512", "uno.extensions.core.winui.nuspec", "uno.png"]}, "Uno.Extensions.Hosting/6.0.12": {"sha512": "6WtLJisut8wWTbIidTUcH+qL33TsTF7ldw2Li7j2tIISDrS2OtXj5NrljO3syH5eQc15gHGSHm+iMmYdm+/ffA==", "type": "package", "path": "uno.extensions.hosting/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Hosting.targets", "lib/net8.0/Uno.Extensions.Hosting.dll", "lib/net8.0/Uno.Extensions.Hosting.pdb", "lib/net8.0/Uno.Extensions.Hosting.xml", "uno.extensions.hosting.6.0.12.nupkg.sha512", "uno.extensions.hosting.nuspec", "uno.png"]}, "Uno.Extensions.Hosting.WinUI/6.0.12": {"sha512": "dhrw/nKevKoLE8XhqBXM4KXbrG6d0TBw2mVJ3fBOATuQ5yj0/PWkdqmIPFVAkhuoSM4aMSkOso8dtsva0AXE0g==", "type": "package", "path": "uno.extensions.hosting.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0-android34.0/Uno.Extensions.Hosting.WinUI.aar", "lib/net8.0-android34.0/Uno.Extensions.Hosting.WinUI.dll", "lib/net8.0-android34.0/Uno.Extensions.Hosting.WinUI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Hosting.WinUI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Hosting.WinUI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Hosting.WinUI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Hosting.WinUI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Hosting.WinUI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Hosting.WinUI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Hosting.WinUI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Hosting.WinUI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Hosting.WinUI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Hosting.WinUI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Hosting.WinUI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Hosting.WinUI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Hosting.WinUI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Hosting.WinUI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Hosting.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Hosting.WinUI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Hosting.WinUI.xml", "lib/net8.0/Uno.Extensions.Hosting.WinUI.dll", "lib/net8.0/Uno.Extensions.Hosting.WinUI.pdb", "lib/net8.0/Uno.Extensions.Hosting.WinUI.xml", "uno.extensions.hosting.winui.6.0.12.nupkg.sha512", "uno.extensions.hosting.winui.nuspec", "uno.png"]}, "Uno.Extensions.Http/6.0.12": {"sha512": "QxnFc/FZRmVFrwPMGBSsxrTMX3Irb6n/9uY2lUYav7aiSYrbrflbap9k7OGw/+NVkEn97NrE5mMRdLQ4f/gjOQ==", "type": "package", "path": "uno.extensions.http/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Http.targets", "lib/net8.0/Uno.Extensions.Http.dll", "lib/net8.0/Uno.Extensions.Http.pdb", "lib/net8.0/Uno.Extensions.Http.xml", "uno.extensions.http.6.0.12.nupkg.sha512", "uno.extensions.http.nuspec", "uno.png"]}, "Uno.Extensions.Http.Kiota/6.0.12": {"sha512": "/7TmFcjpZWBfB8mwW3RuuQA2lPesWD9CAld970Ly6p7Ba9+Aoa8xFyfN6ajAIlAWPgZ6eAWJiK+WyIlpjIa9jA==", "type": "package", "path": "uno.extensions.http.kiota/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Uno.Extensions.Http.Kiota.dll", "lib/net8.0/Uno.Extensions.Http.Kiota.pdb", "lib/net8.0/Uno.Extensions.Http.Kiota.xml", "uno.extensions.http.kiota.6.0.12.nupkg.sha512", "uno.extensions.http.kiota.nuspec", "uno.png"]}, "Uno.Extensions.Http.WinUI/6.0.12": {"sha512": "ooWhQJYSquEa9VyKbwZdRfl+Y7rTfwOJPNglTVNTHFCTRe3gCepKcCEwKszhQgRCjfq/T9jqtI/jXJI3M5DUfg==", "type": "package", "path": "uno.extensions.http.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0-android34.0/Uno.Extensions.Http.WinUI.aar", "lib/net8.0-android34.0/Uno.Extensions.Http.WinUI.dll", "lib/net8.0-android34.0/Uno.Extensions.Http.WinUI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Http.WinUI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Http.WinUI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Http.WinUI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Http.WinUI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Http.WinUI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Http.WinUI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Http.WinUI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Http.WinUI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Http.WinUI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Http.WinUI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Http.WinUI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Http.WinUI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Http.WinUI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Http.WinUI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Http.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Http.WinUI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Http.WinUI.xml", "lib/net8.0/Uno.Extensions.Http.WinUI.dll", "lib/net8.0/Uno.Extensions.Http.WinUI.pdb", "lib/net8.0/Uno.Extensions.Http.WinUI.xml", "uno.extensions.http.winui.6.0.12.nupkg.sha512", "uno.extensions.http.winui.nuspec", "uno.png"]}, "Uno.Extensions.Localization/6.0.12": {"sha512": "JE0PzYqzQfnMSzr2E0SF3gkE0JCcRmXR3N3yYno+PAL9y8PeMV8AnhjAr910Ld1W3Os3aKGfqk4AjE7cpJ2DcA==", "type": "package", "path": "uno.extensions.localization/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Localization.targets", "lib/net8.0/Uno.Extensions.Localization.dll", "lib/net8.0/Uno.Extensions.Localization.pdb", "lib/net8.0/Uno.Extensions.Localization.xml", "uno.extensions.localization.6.0.12.nupkg.sha512", "uno.extensions.localization.nuspec", "uno.png"]}, "Uno.Extensions.Localization.WinUI/6.0.12": {"sha512": "P0h1G0gshUBbvcQEHNF2oxpdmRi36/qs1rO/ToFEwHeWv/Thp1j8Hvp4XqdQ42I1gpKIuREBhfW+Cx4BWt7T0w==", "type": "package", "path": "uno.extensions.localization.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0-android34.0/Uno.Extensions.Localization.WinUI.aar", "lib/net8.0-android34.0/Uno.Extensions.Localization.WinUI.dll", "lib/net8.0-android34.0/Uno.Extensions.Localization.WinUI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Localization.WinUI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Localization.WinUI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Localization.WinUI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Localization.WinUI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Localization.WinUI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Localization.WinUI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Localization.WinUI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Localization.WinUI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Localization.WinUI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Localization.WinUI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Localization.WinUI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Localization.WinUI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Localization.WinUI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Localization.WinUI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Localization.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Localization.WinUI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Localization.WinUI.xml", "lib/net8.0/Uno.Extensions.Localization.WinUI.dll", "lib/net8.0/Uno.Extensions.Localization.WinUI.pdb", "lib/net8.0/Uno.Extensions.Localization.WinUI.xml", "uno.extensions.localization.winui.6.0.12.nupkg.sha512", "uno.extensions.localization.winui.nuspec", "uno.png"]}, "Uno.Extensions.Logging.WebAssembly.Console/1.7.0": {"sha512": "VuDnbM+a/+OkTGvPxQPeYPeZLxWQxKrE55YqwSDxw8lWyo/XwFYrIQmYKTFR8f8/ldrs/Po82YSwfxLnX0he8g==", "type": "package", "path": "uno.extensions.logging.webassembly.console/1.7.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Uno.Extensions.Logging.WebAssembly.Console.dll", "lib/net7.0/Uno.Extensions.Logging.WebAssembly.Console.dll", "lib/netstandard2.0/Uno.Extensions.Logging.WebAssembly.Console.dll", "uno-logo.png", "uno.extensions.logging.webassembly.console.1.7.0.nupkg.sha512", "uno.extensions.logging.webassembly.console.nuspec"]}, "Uno.Extensions.Logging.WinUI/6.0.12": {"sha512": "NKeHZ5LcFXXEM6H6Rf950w2k88pvR4E9QQWxNmOp8SktYxVCnP15ZdDXXdqUF07Z2t6m6Q/UvNv3o55M+QFAKQ==", "type": "package", "path": "uno.extensions.logging.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0-android34.0/Uno.Extensions.Logging.WinUI.aar", "lib/net8.0-android34.0/Uno.Extensions.Logging.WinUI.dll", "lib/net8.0-android34.0/Uno.Extensions.Logging.WinUI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Logging.WinUI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Logging.WinUI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Logging.WinUI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Logging.WinUI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Logging.WinUI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Logging.WinUI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Logging.WinUI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Logging.WinUI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Logging.WinUI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Logging.WinUI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Logging.WinUI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Logging.WinUI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Logging.WinUI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Logging.WinUI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Logging.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Logging.WinUI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Logging.WinUI.xml", "lib/net8.0/Uno.Extensions.Logging.WinUI.dll", "lib/net8.0/Uno.Extensions.Logging.WinUI.pdb", "lib/net8.0/Uno.Extensions.Logging.WinUI.xml", "uno.extensions.logging.winui.6.0.12.nupkg.sha512", "uno.extensions.logging.winui.nuspec", "uno.png"]}, "Uno.Extensions.Navigation/6.0.12": {"sha512": "ZCMOcfOgJfmw6sGg0cYH/CCBkKOgkIOPe4GcTN6Sp5ARMxQrXTiVGpZtZ1EB4i5oSd0laySVu7cvO4JD9Vs5kA==", "type": "package", "path": "uno.extensions.navigation/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Uno.Extensions.Navigation.Generators.dll", "buildTransitive/Uno.Extensions.Navigation.targets", "lib/net8.0/Uno.Extensions.Navigation.dll", "lib/net8.0/Uno.Extensions.Navigation.pdb", "lib/net8.0/Uno.Extensions.Navigation.xml", "uno.extensions.navigation.6.0.12.nupkg.sha512", "uno.extensions.navigation.nuspec", "uno.png"]}, "Uno.Extensions.Navigation.Toolkit.WinUI/6.0.12": {"sha512": "kSPUPDpaZWa9aHv/a/uDw4klqGV5I1ZaM8iQ57p/qBKC42mGCvMTLIGRJE2w78beF0pOIs4TPUUuwcKPGLGMhQ==", "type": "package", "path": "uno.extensions.navigation.toolkit.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Navigation.Toolkit.WinUI.targets", "lib/net8.0-android34.0/Uno.Extensions.Navigation.Toolkit.UI.aar", "lib/net8.0-android34.0/Uno.Extensions.Navigation.Toolkit.UI.dll", "lib/net8.0-android34.0/Uno.Extensions.Navigation.Toolkit.UI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Navigation.Toolkit.UI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Navigation.Toolkit.UI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Navigation.Toolkit.UI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Navigation.Toolkit.UI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Navigation.Toolkit.UI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Navigation.Toolkit.UI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Navigation.Toolkit.UI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Navigation.Toolkit.UI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Navigation.Toolkit.UI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Navigation.Toolkit.UI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Navigation.Toolkit.UI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Navigation.Toolkit.UI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Navigation.Toolkit.UI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.Toolkit.UI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.Toolkit.UI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.Toolkit.UI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.Toolkit.UI.xml", "lib/net8.0/Uno.Extensions.Navigation.Toolkit.UI.dll", "lib/net8.0/Uno.Extensions.Navigation.Toolkit.UI.pdb", "lib/net8.0/Uno.Extensions.Navigation.Toolkit.UI.xml", "uno.extensions.navigation.toolkit.winui.6.0.12.nupkg.sha512", "uno.extensions.navigation.toolkit.winui.nuspec", "uno.png"]}, "Uno.Extensions.Navigation.WinUI/6.0.12": {"sha512": "KcBzV3LXPzLwcS4l7ZyYWQI3YVXCbmZyaE3LEbJe81vEOZCxTSRzjNkMuRzFdiKS75ixwVzmSXC1iIsm3q7iUg==", "type": "package", "path": "uno.extensions.navigation.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Navigation.WinUI.targets", "lib/net8.0-android34.0/Uno.Extensions.Navigation.UI.aar", "lib/net8.0-android34.0/Uno.Extensions.Navigation.UI.dll", "lib/net8.0-android34.0/Uno.Extensions.Navigation.UI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Navigation.UI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Navigation.UI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Navigation.UI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Navigation.UI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Navigation.UI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Navigation.UI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Navigation.UI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Navigation.UI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Navigation.UI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Navigation.UI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Navigation.UI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Navigation.UI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Navigation.UI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.UI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.UI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.UI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Navigation.UI.xml", "lib/net8.0/Uno.Extensions.Navigation.UI.dll", "lib/net8.0/Uno.Extensions.Navigation.UI.pdb", "lib/net8.0/Uno.Extensions.Navigation.UI.xml", "uno.extensions.navigation.winui.6.0.12.nupkg.sha512", "uno.extensions.navigation.winui.nuspec", "uno.png"]}, "Uno.Extensions.Reactive/6.0.12": {"sha512": "/r1QyWdVjekz9yu6//gumVAkEsxsGPqmSQry4y6Csi8c1qhfBENjcIuLAZbR1EJ8Bw6fO3mOKkNkU+zakuAXsA==", "type": "package", "path": "uno.extensions.reactive/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Uno.Extensions.Reactive.Generator.dll", "buildTransitive/Uno.Extensions.Reactive.targets", "lib/net8.0/Uno.Extensions.Reactive.dll", "lib/net8.0/Uno.Extensions.Reactive.pdb", "lib/net8.0/Uno.Extensions.Reactive.xml", "uno.extensions.reactive.6.0.12.nupkg.sha512", "uno.extensions.reactive.nuspec", "uno.png"]}, "Uno.Extensions.Reactive.Messaging/6.0.12": {"sha512": "cqOLFojEPkeV2v9B8tPC6nm0b9hg2UqHnXAa4aRIZLR/hrCMmQ2KMmGcUAnMeGImQZLe1Y10jD5ZcNLDj0XLRw==", "type": "package", "path": "uno.extensions.reactive.messaging/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Reactive.Messaging.targets", "lib/net8.0/Uno.Extensions.Reactive.Messaging.dll", "lib/net8.0/Uno.Extensions.Reactive.Messaging.pdb", "lib/net8.0/Uno.Extensions.Reactive.Messaging.xml", "uno.extensions.reactive.messaging.6.0.12.nupkg.sha512", "uno.extensions.reactive.messaging.nuspec", "uno.png"]}, "Uno.Extensions.Reactive.WinUI/6.0.12": {"sha512": "Y0mt2RkHwF03F3HRVCjzijSE0LM99u3sAgj7jHyT0j5bSPkhnO4xHYiulOQqPf/9ZHJWeqRJf0JRmJHyUbVJ+w==", "type": "package", "path": "uno.extensions.reactive.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Reactive.WinUI.targets", "lib/net8.0-android34.0/Uno.Extensions.Reactive.UI.aar", "lib/net8.0-android34.0/Uno.Extensions.Reactive.UI.dll", "lib/net8.0-android34.0/Uno.Extensions.Reactive.UI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Reactive.UI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Reactive.UI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Reactive.UI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Reactive.UI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Reactive.UI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Reactive.UI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Reactive.UI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Reactive.UI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Reactive.UI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Reactive.UI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Reactive.UI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Reactive.UI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Reactive.UI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Reactive.UI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Reactive.UI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Reactive.UI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Reactive.UI.xml", "lib/net8.0/Uno.Extensions.Reactive.UI.dll", "lib/net8.0/Uno.Extensions.Reactive.UI.pdb", "lib/net8.0/Uno.Extensions.Reactive.UI.xml", "uno.extensions.reactive.winui.6.0.12.nupkg.sha512", "uno.extensions.reactive.winui.nuspec", "uno.png"]}, "Uno.Extensions.Serialization/6.0.12": {"sha512": "FCi35LidQ0gAW+BqzbIRlhLkGH9NGbAi9udvYAWMQtw3XIq+hxU3OkavKW6Iu/b1c9XU1HTE5OkTpaVZXmKswQ==", "type": "package", "path": "uno.extensions.serialization/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Serialization.targets", "lib/net8.0/Uno.Extensions.Serialization.dll", "lib/net8.0/Uno.Extensions.Serialization.pdb", "lib/net8.0/Uno.Extensions.Serialization.xml", "uno.extensions.serialization.6.0.12.nupkg.sha512", "uno.extensions.serialization.nuspec", "uno.png"]}, "Uno.Extensions.Serialization.Http/6.0.12": {"sha512": "gZpSzblLgM5z9EzBwbDBUFbL1O6UMXrNqDWxe5MmYTuU9pkXwGfHLP1BpLpLucbELZqpr1xsAm5HIPBGXX25CA==", "type": "package", "path": "uno.extensions.serialization.http/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Serialization.Http.targets", "lib/net8.0/Uno.Extensions.Serialization.Http.dll", "lib/net8.0/Uno.Extensions.Serialization.Http.pdb", "lib/net8.0/Uno.Extensions.Serialization.Http.xml", "uno.extensions.serialization.http.6.0.12.nupkg.sha512", "uno.extensions.serialization.http.nuspec", "uno.png"]}, "Uno.Extensions.Serialization.Refit/6.0.12": {"sha512": "W6ZceQLCwpzI1qlWHpd01uDsLkOfZFskT+WlKhnbUSc/VMS2ToJhQGKAe7bg5cmdBPZvlkZObgVYOiA3i13SEg==", "type": "package", "path": "uno.extensions.serialization.refit/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Serialization.Refit.targets", "lib/net8.0/Uno.Extensions.Serialization.Refit.dll", "lib/net8.0/Uno.Extensions.Serialization.Refit.pdb", "lib/net8.0/Uno.Extensions.Serialization.Refit.xml", "uno.extensions.serialization.refit.6.0.12.nupkg.sha512", "uno.extensions.serialization.refit.nuspec", "uno.png"]}, "Uno.Extensions.Storage/6.0.12": {"sha512": "wXZ1Gojabo7CqZSWXRCJEQ/1aBJygVPmaKANQ8zJKbgR1b0oLaNXBtOFOiiyx6MzKeEbbiTNHhl0ojoqApyJZQ==", "type": "package", "path": "uno.extensions.storage/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Storage.targets", "lib/net8.0/Uno.Extensions.Storage.dll", "lib/net8.0/Uno.Extensions.Storage.pdb", "lib/net8.0/Uno.Extensions.Storage.xml", "uno.extensions.storage.6.0.12.nupkg.sha512", "uno.extensions.storage.nuspec", "uno.png"]}, "Uno.Extensions.Storage.WinUI/6.0.12": {"sha512": "P+DBRfIpy7B3gtO4tu1fy9IOHbNme4l+HoxksW4lgdSCeLbmaIynx5T+bi+AToftdYMSGSjV2UVxEbw/759VxQ==", "type": "package", "path": "uno.extensions.storage.winui/6.0.12", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Extensions.Storage.WinUI.targets", "lib/net8.0-android34.0/Uno.Extensions.Storage.UI.aar", "lib/net8.0-android34.0/Uno.Extensions.Storage.UI.dll", "lib/net8.0-android34.0/Uno.Extensions.Storage.UI.pdb", "lib/net8.0-android34.0/Uno.Extensions.Storage.UI.xml", "lib/net8.0-browserwasm1.0/Uno.Extensions.Storage.UI.dll", "lib/net8.0-browserwasm1.0/Uno.Extensions.Storage.UI.pdb", "lib/net8.0-browserwasm1.0/Uno.Extensions.Storage.UI.xml", "lib/net8.0-desktop1.0/Uno.Extensions.Storage.UI.dll", "lib/net8.0-desktop1.0/Uno.Extensions.Storage.UI.pdb", "lib/net8.0-desktop1.0/Uno.Extensions.Storage.UI.xml", "lib/net8.0-ios18.0/Uno.Extensions.Storage.UI.dll", "lib/net8.0-ios18.0/Uno.Extensions.Storage.UI.pdb", "lib/net8.0-ios18.0/Uno.Extensions.Storage.UI.xml", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Storage.UI.dll", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Storage.UI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Extensions.Storage.UI.xml", "lib/net8.0-windows10.0.19041/Uno.Extensions.Storage.UI.dll", "lib/net8.0-windows10.0.19041/Uno.Extensions.Storage.UI.pdb", "lib/net8.0-windows10.0.19041/Uno.Extensions.Storage.UI.pri", "lib/net8.0-windows10.0.19041/Uno.Extensions.Storage.UI.xml", "lib/net8.0/Uno.Extensions.Storage.UI.dll", "lib/net8.0/Uno.Extensions.Storage.UI.pdb", "lib/net8.0/Uno.Extensions.Storage.UI.xml", "uno.extensions.storage.winui.6.0.12.nupkg.sha512", "uno.extensions.storage.winui.nuspec", "uno.png"]}, "Uno.Fonts.Fluent/2.6.1": {"sha512": "mV1JWlpU9e6uWf/hamfTv+ysSRlRjjao1WtewGUtFz2/tB83C5h10gZReiWX4ZQXG2P0bgI/yx1ZxPYfiM3Hcg==", "type": "package", "path": "uno.fonts.fluent/2.6.1", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Fonts.Fluent.props", "icon.png", "lib/netstandard1.0/Uno.Fonts.Fluent.dll", "lib/netstandard1.0/Uno.Fonts.Fluent.uprimarker", "lib/netstandard1.0/Uno.Fonts.Fluent/Fonts/uno-fluentui-assets.ttf", "uno.fonts.fluent.2.6.1.nupkg.sha512", "uno.fonts.fluent.nuspec"]}, "Uno.Fonts.OpenSans/2.7.1": {"sha512": "sFZUzCtp7K95kw2BPeFvuNF3RgBgFGAmjkPFGkSwZJwcSk368PW76gCaVYrRBe4niQHOsQg84xmE+PtoYvpVog==", "type": "package", "path": "uno.fonts.opensans/2.7.1", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net7.0/Uno.Fonts.OpenSans.targets", "icon.png", "lib/net7.0/Uno.Fonts.OpenSans.dll", "lib/net7.0/Uno.Fonts.OpenSans.uprimarker", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-Bold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-BoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-ExtraBold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-ExtraBoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-Italic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-Light.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-LightItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-Medium.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-MediumItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-Regular.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-SemiBold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans-SemiBoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans.ttf.manifest", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Bold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-BoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-ExtraBold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-ExtraBoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Italic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Light.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-LightItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Medium.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-MediumItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Regular.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-SemiBold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-SemiBoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Bold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-BoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-ExtraBold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-ExtraBoldItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Italic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Light.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-LightItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Medium.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-MediumItalic.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Regular.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-SemiBold.ttf", "lib/net7.0/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-SemiBoldItalic.ttf", "uno.fonts.opensans.2.7.1.nupkg.sha512", "uno.fonts.opensans.nuspec"]}, "Uno.Fonts.Roboto/2.2.2": {"sha512": "ZwpuV0++EPb7qAZuk8rj9fB4DR8FjeAcjug/JRYYf45p2tep6yb2GGxhU0yMGG+57Dtjavomq+abOXetSKEKvw==", "type": "package", "path": "uno.fonts.roboto/2.2.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0-windows10.0.18362/Uno.Fonts.Roboto.dll", "lib/net6.0-windows10.0.18362/Uno.Fonts.Roboto.pri", "lib/net6.0-windows10.0.18362/Uno.Fonts.Roboto/Fonts/Roboto-Light.ttf", "lib/net6.0-windows10.0.18362/Uno.Fonts.Roboto/Fonts/Roboto-Medium.ttf", "lib/net6.0-windows10.0.18362/Uno.Fonts.Roboto/Fonts/Roboto-Regular.ttf", "lib/netstandard2.0/Uno.Fonts.Roboto.dll", "lib/netstandard2.0/Uno.Fonts.Roboto.uprimarker", "lib/netstandard2.0/Uno.Fonts.Roboto/Fonts/Roboto-Light.ttf", "lib/netstandard2.0/Uno.Fonts.Roboto/Fonts/Roboto-Medium.ttf", "lib/netstandard2.0/Uno.Fonts.Roboto/Fonts/Roboto-Regular.ttf", "lib/uap10.0.18362/Uno.Fonts.Roboto.dll", "lib/uap10.0.18362/Uno.Fonts.Roboto.pri", "lib/uap10.0.18362/Uno.Fonts.Roboto/Fonts/Roboto-Light.ttf", "lib/uap10.0.18362/Uno.Fonts.Roboto/Fonts/Roboto-Medium.ttf", "lib/uap10.0.18362/Uno.Fonts.Roboto/Fonts/Roboto-Regular.ttf", "uno.fonts.roboto.2.2.2.nupkg.sha512", "uno.fonts.roboto.nuspec"]}, "Uno.Foundation/6.0.797": {"sha512": "qS+o3wL6DwYP37avSn7BJAkMD2hBCv0pFRrnB4v/q57+S7BvEaR/fDs28X/bEXi3eUjR48ggm2j3LWoGkNqNLg==", "type": "package", "path": "uno.foundation/6.0.797", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Foundation.props", "lib/net8.0-android30.0/Uno.Foundation.dll", "lib/net8.0-android30.0/Uno.Foundation.pdb", "lib/net8.0-ios17.0/Uno.Foundation.dll", "lib/net8.0-ios17.0/Uno.Foundation.pdb", "lib/net8.0-maccatalyst17.0/Uno.Foundation.dll", "lib/net8.0-maccatalyst17.0/Uno.Foundation.pdb", "lib/net8.0-tvos17.0/Uno.Foundation.dll", "lib/net8.0-tvos17.0/Uno.Foundation.pdb", "lib/net8.0/Uno.Foundation.dll", "lib/net9.0-android30.0/Uno.Foundation.dll", "lib/net9.0-android30.0/Uno.Foundation.pdb", "lib/net9.0-ios18.0/Uno.Foundation.dll", "lib/net9.0-ios18.0/Uno.Foundation.pdb", "lib/net9.0-maccatalyst18.0/Uno.Foundation.dll", "lib/net9.0-maccatalyst18.0/Uno.Foundation.pdb", "lib/net9.0-tvos18.0/Uno.Foundation.dll", "lib/net9.0-tvos18.0/Uno.Foundation.pdb", "lib/net9.0/Uno.Foundation.dll", "tools/_._", "uno-runtime/net8.0/skia/Uno.Foundation.dll", "uno-runtime/net8.0/skia/Uno.Foundation.pdb", "uno-runtime/net8.0/webassembly/Uno.Foundation.dll", "uno-runtime/net8.0/webassembly/Uno.Foundation.pdb", "uno-runtime/net9.0/skia/Uno.Foundation.dll", "uno-runtime/net9.0/skia/Uno.Foundation.pdb", "uno-runtime/net9.0/webassembly/Uno.Foundation.dll", "uno-runtime/net9.0/webassembly/Uno.Foundation.pdb", "uno.foundation.6.0.797.nupkg.sha512", "uno.foundation.nuspec", "uno.png"]}, "Uno.Foundation.Logging/6.0.797": {"sha512": "SoS/z8zSwXK3YPrXNOwsy5N0wI24KnSdx5IF6OswqAAJdebgm7RrPMfADPG02T28I01eruoPtxT9qfGfMjFdTQ==", "type": "package", "path": "uno.foundation.logging/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Uno.Foundation.Logging.dll", "lib/net8.0/Uno.Foundation.Logging.pdb", "uno.foundation.logging.6.0.797.nupkg.sha512", "uno.foundation.logging.nuspec", "uno.png"]}, "Uno.Material.WinUI/5.5.4": {"sha512": "0VMTW4pRKfPZZbT0lozGLI2ejZ7kMQwaPdAzLZ6KY8tUgCup2ROlw2cTcjkN6dOK+RxbMx2IKICT97gRTK1yCg==", "type": "package", "path": "uno.material.winui/5.5.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "buildTransitive/Uno.Material.WinUI.targets", "lib/net8.0-android34.0/Uno.Material.WinUI.aar", "lib/net8.0-android34.0/Uno.Material.WinUI.dll", "lib/net8.0-android34.0/Uno.Material.WinUI.pdb", "lib/net8.0-android34.0/Uno.Material.WinUI.xml", "lib/net8.0-ios18.0/Uno.Material.WinUI.dll", "lib/net8.0-ios18.0/Uno.Material.WinUI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Material.WinUI.dll", "lib/net8.0-maccatalyst18.0/Uno.Material.WinUI.pdb", "lib/net8.0-macos15.0/Uno.Material.WinUI.dll", "lib/net8.0-macos15.0/Uno.Material.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Material.WinUI.dll", "lib/net8.0-windows10.0.19041/Uno.Material.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Material.WinUI.pri", "lib/net8.0-windows10.0.19041/Uno.Material.WinUI/Assets/MaterialDeterminate.json", "lib/net8.0-windows10.0.19041/Uno.Material.WinUI/Assets/MaterialIndeterminate.json", "lib/net8.0/Uno.Material.WinUI.dll", "lib/net8.0/Uno.Material.WinUI.pdb", "uno-logo.png", "uno.material.winui.5.5.4.nupkg.sha512", "uno.material.winui.nuspec", "uno.png"]}, "Uno.Resizetizer/1.8.1": {"sha512": "TeHaEA2v5SN7cr98BAN8Tfm7xKKpTwzL9KBFF8+FzK9Vfc8tuOw1mwetorc8nArXN9kseXufyZIfqWk+kEKeAQ==", "type": "package", "path": "uno.resizetizer/1.8.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Uno.Resizetizer.android.targets", "build/Uno.Resizetizer.apple.targets", "build/Uno.Resizetizer.targets", "build/Uno.Resizetizer.wasm.targets", "build/Uno.Resizetizer.windows.skia.targets", "build/netstandard2.0/ExCSS.dll", "build/netstandard2.0/Fizzler.dll", "build/netstandard2.0/HarfBuzzSharp.dll", "build/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "build/netstandard2.0/Microsoft.Build.Framework.dll", "build/netstandard2.0/Microsoft.Build.Utilities.Core.dll", "build/netstandard2.0/ShimSkiaSharp.dll", "build/netstandard2.0/SkiaSharp.HarfBuzz.dll", "build/netstandard2.0/SkiaSharp.dll", "build/netstandard2.0/Svg.Custom.dll", "build/netstandard2.0/Svg.Model.dll", "build/netstandard2.0/Svg.Skia.dll", "build/netstandard2.0/System.Buffers.dll", "build/netstandard2.0/System.Collections.Immutable.dll", "build/netstandard2.0/System.IO.FileSystem.Primitives.dll", "build/netstandard2.0/System.IO.UnmanagedMemoryStream.dll", "build/netstandard2.0/System.Memory.dll", "build/netstandard2.0/System.Numerics.Vectors.dll", "build/netstandard2.0/System.ObjectModel.dll", "build/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "build/netstandard2.0/System.Security.AccessControl.dll", "build/netstandard2.0/System.Security.Permissions.dll", "build/netstandard2.0/System.Security.Principal.Windows.dll", "build/netstandard2.0/System.Text.Encodings.Web.dll", "build/netstandard2.0/System.Text.Json.dll", "build/netstandard2.0/System.Threading.Tasks.Extensions.dll", "build/netstandard2.0/System.Threading.dll", "build/netstandard2.0/Uno.Resizetizer_6ff47475889da29ead390910ef445cbda3395995.dll", "build/netstandard2.0/runtimes/linux-arm/native/libHarfBuzzSharp.so", "build/netstandard2.0/runtimes/linux-arm/native/libSkiaSharp.so", "build/netstandard2.0/runtimes/linux-arm64/native/libHarfBuzzSharp.so", "build/netstandard2.0/runtimes/linux-arm64/native/libSkiaSharp.so", "build/netstandard2.0/runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "build/netstandard2.0/runtimes/linux-musl-x64/native/libSkiaSharp.so", "build/netstandard2.0/runtimes/linux-x64/native/libHarfBuzzSharp.so", "build/netstandard2.0/runtimes/linux-x64/native/libSkiaSharp.so", "build/netstandard2.0/runtimes/osx/native/libHarfBuzzSharp.dylib", "build/netstandard2.0/runtimes/osx/native/libSkiaSharp.dylib", "build/netstandard2.0/runtimes/win-arm64/native/libHarfBuzzSharp.dll", "build/netstandard2.0/runtimes/win-arm64/native/libSkiaSharp.dll", "build/netstandard2.0/runtimes/win-x64/native/libHarfBuzzSharp.dll", "build/netstandard2.0/runtimes/win-x64/native/libSkiaSharp.dll", "build/netstandard2.0/runtimes/win-x86/native/libHarfBuzzSharp.dll", "build/netstandard2.0/runtimes/win-x86/native/libSkiaSharp.dll", "uno.png", "uno.resizetizer.1.8.1.nupkg.sha512", "uno.resizetizer.nuspec"]}, "Uno.Sdk.Extras/5.6.3": {"sha512": "slDSWKFsOJHljhR/WEy00Xk/g541MC9bPkZQv8ajniCz8F+jb7bveNmYxt0zrJB+Exp3j77PqWdB2oAOZcJDeA==", "type": "package", "path": "uno.sdk.extras/5.6.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.md", "README.md", "ThirdPartyNotices.txt", "buildTransitive/Uno.Sdk.Extras.Publish.Linux.targets", "buildTransitive/Uno.Sdk.Extras.Publish.MacOS.targets", "buildTransitive/Uno.Sdk.Extras.Publish.Windows.targets", "buildTransitive/Uno.Sdk.Extras.Publish.targets", "buildTransitive/Uno.Sdk.Extras.props", "buildTransitive/Uno.Sdk.Extras.targets", "buildTransitive/netstandard2.0/Uno.Sdk.Extras_vb5ec6eb91d.dll", "uno.sdk.extras.5.6.3.nupkg.sha512", "uno.sdk.extras.nuspec"]}, "Uno.Settings.DevServer/1.3.12": {"sha512": "k8Q0vIogOlbvOi0u1mlCfTJEFi9qjhsW3gouOn4zGX/b6xobmKhfD5/LSqnG+7udMHa3xSlmWPm8HDrJzaZJ2A==", "type": "package", "path": "uno.settings.devserver/1.3.12", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Settings.DevServer.targets", "tools/devserver/Config/auth.json", "tools/devserver/Config/sdk.json", "tools/devserver/IdentityModel.OidcClient.dll", "tools/devserver/IdentityModel.dll", "tools/devserver/Microsoft.ApplicationInsights.dll", "tools/devserver/Microsoft.Bcl.TimeProvider.dll", "tools/devserver/Microsoft.DotNet.PlatformAbstractions.dll", "tools/devserver/Microsoft.Extensions.Configuration.Abstractions.dll", "tools/devserver/Microsoft.Extensions.Configuration.Binder.dll", "tools/devserver/Microsoft.Extensions.Configuration.FileExtensions.dll", "tools/devserver/Microsoft.Extensions.Configuration.Json.dll", "tools/devserver/Microsoft.Extensions.Configuration.dll", "tools/devserver/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/devserver/Microsoft.Extensions.Diagnostics.Abstractions.dll", "tools/devserver/Microsoft.Extensions.FileProviders.Abstractions.dll", "tools/devserver/Microsoft.Extensions.FileProviders.Physical.dll", "tools/devserver/Microsoft.Extensions.FileSystemGlobbing.dll", "tools/devserver/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/devserver/Microsoft.Extensions.Logging.Abstractions.dll", "tools/devserver/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "tools/devserver/Microsoft.Extensions.Options.dll", "tools/devserver/Microsoft.Extensions.Primitives.dll", "tools/devserver/Microsoft.IdentityModel.Abstractions.dll", "tools/devserver/Microsoft.IdentityModel.JsonWebTokens.dll", "tools/devserver/Microsoft.IdentityModel.Logging.dll", "tools/devserver/Microsoft.IdentityModel.Tokens.dll", "tools/devserver/Microsoft.Kiota.Abstractions.dll", "tools/devserver/Microsoft.Kiota.Http.HttpClientLibrary.dll", "tools/devserver/Microsoft.Kiota.Serialization.Form.dll", "tools/devserver/Microsoft.Kiota.Serialization.Json.dll", "tools/devserver/Microsoft.Kiota.Serialization.Multipart.dll", "tools/devserver/Microsoft.Kiota.Serialization.Text.dll", "tools/devserver/Newtonsoft.Json.dll", "tools/devserver/Std.UriTemplate.dll", "tools/devserver/Uno.DevTools.Telemetry.dll", "tools/devserver/Uno.Licensing.Common.dll", "tools/devserver/Uno.Licensing.Common.pdb", "tools/devserver/Uno.Licensing.Common.xml", "tools/devserver/Uno.Licensing.Sdk.Contracts.dll", "tools/devserver/Uno.Licensing.Sdk.Contracts.pdb", "tools/devserver/Uno.Licensing.Sdk.Contracts.xml", "tools/devserver/Uno.Licensing.Sdk.dll", "tools/devserver/Uno.Licensing.Sdk.pdb", "tools/devserver/Uno.Licensing.Sdk.xml", "tools/devserver/Uno.Settings.DevServer.deps.json", "tools/devserver/Uno.Settings.DevServer.dll", "tools/devserver/Uno.Settings.DevServer.pdb", "tools/devserver/Uno.Settings.DevServer.xml", "tools/devserver/Uno.UI.RemoteControl.Messaging.dll", "tools/manager/Assets/Icons/iconLargeTile.scale-100.png", "tools/manager/Assets/Icons/iconLargeTile.scale-125.png", "tools/manager/Assets/Icons/iconLargeTile.scale-150.png", "tools/manager/Assets/Icons/iconLargeTile.scale-200.png", "tools/manager/Assets/Icons/iconLargeTile.scale-400.png", "tools/manager/Assets/Icons/iconLogo.altform-lightunplated_targetsize-16.png", "tools/manager/Assets/Icons/iconLogo.altform-lightunplated_targetsize-24.png", "tools/manager/Assets/Icons/iconLogo.altform-lightunplated_targetsize-256.png", "tools/manager/Assets/Icons/iconLogo.altform-lightunplated_targetsize-32.png", "tools/manager/Assets/Icons/iconLogo.altform-lightunplated_targetsize-48.png", "tools/manager/Assets/Icons/iconLogo.altform-unplated_targetsize-16.png", "tools/manager/Assets/Icons/iconLogo.altform-unplated_targetsize-24.png", "tools/manager/Assets/Icons/iconLogo.altform-unplated_targetsize-256.png", "tools/manager/Assets/Icons/iconLogo.altform-unplated_targetsize-32.png", "tools/manager/Assets/Icons/iconLogo.altform-unplated_targetsize-48.png", "tools/manager/Assets/Icons/iconLogo.scale-100.png", "tools/manager/Assets/Icons/iconLogo.scale-125.png", "tools/manager/Assets/Icons/iconLogo.scale-150.png", "tools/manager/Assets/Icons/iconLogo.scale-200.png", "tools/manager/Assets/Icons/iconLogo.scale-400.png", "tools/manager/Assets/Icons/iconLogo.targetsize-16.png", "tools/manager/Assets/Icons/iconLogo.targetsize-24.png", "tools/manager/Assets/Icons/iconLogo.targetsize-256.png", "tools/manager/Assets/Icons/iconLogo.targetsize-32.png", "tools/manager/Assets/Icons/iconLogo.targetsize-48.png", "tools/manager/Assets/Icons/iconMediumTile.scale-100.png", "tools/manager/Assets/Icons/iconMediumTile.scale-125.png", "tools/manager/Assets/Icons/iconMediumTile.scale-150.png", "tools/manager/Assets/Icons/iconMediumTile.scale-200.png", "tools/manager/Assets/Icons/iconMediumTile.scale-400.png", "tools/manager/Assets/Icons/iconSmallTile.scale-100.png", "tools/manager/Assets/Icons/iconSmallTile.scale-125.png", "tools/manager/Assets/Icons/iconSmallTile.scale-150.png", "tools/manager/Assets/Icons/iconSmallTile.scale-200.png", "tools/manager/Assets/Icons/iconSmallTile.scale-400.png", "tools/manager/Assets/Icons/iconStoreLogo.scale-100.png", "tools/manager/Assets/Icons/iconStoreLogo.scale-125.png", "tools/manager/Assets/Icons/iconStoreLogo.scale-150.png", "tools/manager/Assets/Icons/iconStoreLogo.scale-200.png", "tools/manager/Assets/Icons/iconStoreLogo.scale-400.png", "tools/manager/Assets/Icons/iconWideTile.scale-100.png", "tools/manager/Assets/Icons/iconWideTile.scale-125.png", "tools/manager/Assets/Icons/iconWideTile.scale-150.png", "tools/manager/Assets/Icons/iconWideTile.scale-200.png", "tools/manager/Assets/Icons/iconWideTile.scale-400.png", "tools/manager/Assets/Icons/icon_foreground.png", "tools/manager/Assets/Icons/icon_foreground.scale-100.png", "tools/manager/Assets/Icons/icon_foreground.scale-125.png", "tools/manager/Assets/Icons/icon_foreground.scale-150.png", "tools/manager/Assets/Icons/icon_foreground.scale-200.png", "tools/manager/Assets/Icons/icon_foreground.scale-300.png", "tools/manager/Assets/Icons/icon_foreground.scale-400.png", "tools/manager/Assets/Images/back.png", "tools/manager/Assets/Images/back.scale-100.png", "tools/manager/Assets/Images/back.scale-125.png", "tools/manager/Assets/Images/back.scale-150.png", "tools/manager/Assets/Images/back.scale-200.png", "tools/manager/Assets/Images/back.scale-300.png", "tools/manager/Assets/Images/back.scale-400.png", "tools/manager/Assets/Images/logo.png", "tools/manager/Assets/Images/logo.scale-100.png", "tools/manager/Assets/Images/logo.scale-125.png", "tools/manager/Assets/Images/logo.scale-150.png", "tools/manager/Assets/Images/logo.scale-200.png", "tools/manager/Assets/Images/logo.scale-300.png", "tools/manager/Assets/Images/logo.scale-400.png", "tools/manager/Assets/SharedAssets.md", "tools/manager/Assets/Svg/error.svg", "tools/manager/CommonServiceLocator.dll", "tools/manager/CommunityToolkit.Mvvm.dll", "tools/manager/Config/auth.json", "tools/manager/Config/sdk.json", "tools/manager/ExCSS.dll", "tools/manager/HarfBuzzSharp.dll", "tools/manager/IdentityModel.OidcClient.dll", "tools/manager/IdentityModel.dll", "tools/manager/Microsoft.ApplicationInsights.dll", "tools/manager/Microsoft.AspNetCore.Server.Kestrel.Https.dll", "tools/manager/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll", "tools/manager/Microsoft.Bcl.AsyncInterfaces.dll", "tools/manager/Microsoft.Bcl.TimeProvider.dll", "tools/manager/Microsoft.CodeAnalysis.dll", "tools/manager/Microsoft.DotNet.PlatformAbstractions.dll", "tools/manager/Microsoft.Extensions.Configuration.Binder.dll", "tools/manager/Microsoft.Extensions.Configuration.FileExtensions.dll", "tools/manager/Microsoft.Extensions.Configuration.Json.dll", "tools/manager/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/manager/Microsoft.Extensions.DependencyInjection.dll", "tools/manager/Microsoft.Extensions.DependencyModel.dll", "tools/manager/Microsoft.Extensions.Diagnostics.Abstractions.dll", "tools/manager/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/manager/Microsoft.Extensions.Localization.Abstractions.dll", "tools/manager/Microsoft.Extensions.Logging.Abstractions.dll", "tools/manager/Microsoft.Extensions.Logging.Configuration.dll", "tools/manager/Microsoft.Extensions.Logging.Console.dll", "tools/manager/Microsoft.Extensions.Logging.dll", "tools/manager/Microsoft.Extensions.Options.dll", "tools/manager/Microsoft.IdentityModel.Abstractions.dll", "tools/manager/Microsoft.IdentityModel.JsonWebTokens.dll", "tools/manager/Microsoft.IdentityModel.Logging.dll", "tools/manager/Microsoft.IdentityModel.Tokens.dll", "tools/manager/Microsoft.Kiota.Abstractions.dll", "tools/manager/Microsoft.Kiota.Http.HttpClientLibrary.dll", "tools/manager/Microsoft.Kiota.Serialization.Form.dll", "tools/manager/Microsoft.Kiota.Serialization.Json.dll", "tools/manager/Microsoft.Kiota.Serialization.Multipart.dll", "tools/manager/Microsoft.Kiota.Serialization.Text.dll", "tools/manager/Microsoft.Win32.SystemEvents.dll", "tools/manager/Refit.HttpClientFactory.dll", "tools/manager/Refit.dll", "tools/manager/Serilog.Extensions.Hosting.dll", "tools/manager/Serilog.Extensions.Logging.dll", "tools/manager/Serilog.Settings.Configuration.dll", "tools/manager/Serilog.Sinks.Console.dll", "tools/manager/Serilog.Sinks.Debug.dll", "tools/manager/Serilog.Sinks.File.dll", "tools/manager/Serilog.dll", "tools/manager/ShimSkiaSharp.dll", "tools/manager/SkiaSharp.HarfBuzz.dll", "tools/manager/SkiaSharp.SceneGraph.dll", "tools/manager/SkiaSharp.Skottie.dll", "tools/manager/SkiaSharp.Views.Desktop.Common.dll", "tools/manager/SkiaSharp.Views.WPF.dll", "tools/manager/SkiaSharp.Views.Windows.dll", "tools/manager/SkiaSharp.Views.Windows.pdb", "tools/manager/SkiaSharp.dll", "tools/manager/Std.UriTemplate.dll", "tools/manager/Svg.Custom.dll", "tools/manager/Svg.Model.dll", "tools/manager/Svg.Skia.dll", "tools/manager/System.Drawing.Common.dll", "tools/manager/System.Json.dll", "tools/manager/System.Linq.Async.dll", "tools/manager/System.Runtime.InteropServices.WindowsRuntime.dll", "tools/manager/Tmds.DBus.dll", "tools/manager/Uno.Core.Extensions.Collections.dll", "tools/manager/Uno.Core.Extensions.Disposables.dll", "tools/manager/Uno.Core.Extensions.Equality.dll", "tools/manager/Uno.Core.Extensions.Logging.Singleton.dll", "tools/manager/Uno.Core.Extensions.Logging.dll", "tools/manager/Uno.Core.Extensions.dll", "tools/manager/Uno.DevTools.Telemetry.dll", "tools/manager/Uno.Diagnostics.Eventing.dll", "tools/manager/Uno.Extensions.Configuration.dll", "tools/manager/Uno.Extensions.Core.UI.dll", "tools/manager/Uno.Extensions.Core.dll", "tools/manager/Uno.Extensions.Hosting.WinUI.dll", "tools/manager/Uno.Extensions.Hosting.dll", "tools/manager/Uno.Extensions.Http.Refit.dll", "tools/manager/Uno.Extensions.Http.WinUI.dll", "tools/manager/Uno.Extensions.Http.dll", "tools/manager/Uno.Extensions.Localization.WinUI.dll", "tools/manager/Uno.Extensions.Localization.dll", "tools/manager/Uno.Extensions.Logging.Serilog.dll", "tools/manager/Uno.Extensions.Logging.WinUI.dll", "tools/manager/Uno.Extensions.Navigation.Toolkit.UI.dll", "tools/manager/Uno.Extensions.Navigation.UI.dll", "tools/manager/Uno.Extensions.Navigation.dll", "tools/manager/Uno.Extensions.Reactive.Messaging.dll", "tools/manager/Uno.Extensions.Reactive.UI.dll", "tools/manager/Uno.Extensions.Reactive.dll", "tools/manager/Uno.Extensions.Serialization.Http.dll", "tools/manager/Uno.Extensions.Serialization.Refit.dll", "tools/manager/Uno.Extensions.Serialization.dll", "tools/manager/Uno.Extensions.Storage.UI.dll", "tools/manager/Uno.Extensions.Storage.dll", "tools/manager/Uno.Fonts.Fluent.dll", "tools/manager/Uno.Fonts.Fluent/Fonts/uno-fluentui-assets.ttf", "tools/manager/Uno.Fonts.OpenSans.dll", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-Bold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-BoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-ExtraBold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-ExtraBoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-Italic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-Light.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-LightItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-Medium.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-MediumItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-Regular.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-SemiBold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans-SemiBoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans.ttf.manifest", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Bold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-BoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-ExtraBold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-ExtraBoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Italic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Light.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-LightItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Medium.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-MediumItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Regular.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-SemiBold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-SemiBoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Bold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-BoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-ExtraBold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-ExtraBoldItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Italic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Light.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-LightItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Medium.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-MediumItalic.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Regular.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-SemiBold.ttf", "tools/manager/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-SemiBoldItalic.ttf", "tools/manager/Uno.Foundation.Logging.dll", "tools/manager/Uno.Foundation.dll", "tools/manager/Uno.Foundation.pdb", "tools/manager/Uno.Licensing.Common.dll", "tools/manager/Uno.Licensing.Common.pdb", "tools/manager/Uno.Licensing.Common.xml", "tools/manager/Uno.Licensing.Sdk.Contracts.dll", "tools/manager/Uno.Licensing.Sdk.Contracts.pdb", "tools/manager/Uno.Licensing.Sdk.Contracts.xml", "tools/manager/Uno.Licensing.Sdk.dll", "tools/manager/Uno.Licensing.Sdk.pdb", "tools/manager/Uno.Licensing.Sdk.xml", "tools/manager/Uno.Settings.deps.json", "tools/manager/Uno.Settings.dll", "tools/manager/Uno.Settings.exe", "tools/manager/Uno.Settings.pdb", "tools/manager/Uno.Settings.runtimeconfig.json", "tools/manager/Uno.Toolkit.WinUI.dll", "tools/manager/Uno.Toolkit.dll", "tools/manager/Uno.UI.Adapter.Microsoft.Extensions.Logging.dll", "tools/manager/Uno.UI.Composition.dll", "tools/manager/Uno.UI.Composition.pdb", "tools/manager/Uno.UI.Dispatching.dll", "tools/manager/Uno.UI.Dispatching.pdb", "tools/manager/Uno.UI.FluentTheme.dll", "tools/manager/Uno.UI.FluentTheme.pdb", "tools/manager/Uno.UI.FluentTheme.v1.dll", "tools/manager/Uno.UI.FluentTheme.v1.pdb", "tools/manager/Uno.UI.FluentTheme.v2.dll", "tools/manager/Uno.UI.FluentTheme.v2.pdb", "tools/manager/Uno.UI.Lottie.dll", "tools/manager/Uno.UI.Lottie.pdb", "tools/manager/Uno.UI.Runtime.Skia.Linux.FrameBuffer.dll", "tools/manager/Uno.UI.Runtime.Skia.MacOS.dll", "tools/manager/Uno.UI.Runtime.Skia.Wpf.dll", "tools/manager/Uno.UI.Runtime.Skia.X11.dll", "tools/manager/Uno.UI.Runtime.Skia.dll", "tools/manager/Uno.UI.Svg.dll", "tools/manager/Uno.UI.Svg.pdb", "tools/manager/Uno.UI.Toolkit.dll", "tools/manager/Uno.UI.Toolkit.pdb", "tools/manager/Uno.UI.dll", "tools/manager/Uno.UI.pdb", "tools/manager/Uno.WinUI.Graphics2DSK.dll", "tools/manager/Uno.Xaml.dll", "tools/manager/Uno.Xaml.pdb", "tools/manager/Uno.dll", "tools/manager/Uno.pdb", "tools/manager/cs/Microsoft.CodeAnalysis.resources.dll", "tools/manager/de/Microsoft.CodeAnalysis.resources.dll", "tools/manager/es/Microsoft.CodeAnalysis.resources.dll", "tools/manager/fr/Microsoft.CodeAnalysis.resources.dll", "tools/manager/icon.ico", "tools/manager/it/Microsoft.CodeAnalysis.resources.dll", "tools/manager/ja/Microsoft.CodeAnalysis.resources.dll", "tools/manager/ko/Microsoft.CodeAnalysis.resources.dll", "tools/manager/pl/Microsoft.CodeAnalysis.resources.dll", "tools/manager/pt-BR/Microsoft.CodeAnalysis.resources.dll", "tools/manager/ru/Microsoft.CodeAnalysis.resources.dll", "tools/manager/runtimes/linux-arm/native/libHarfBuzzSharp.so", "tools/manager/runtimes/linux-arm/native/libSkiaSharp.so", "tools/manager/runtimes/linux-arm64/native/libHarfBuzzSharp.so", "tools/manager/runtimes/linux-arm64/native/libSkiaSharp.so", "tools/manager/runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "tools/manager/runtimes/linux-musl-x64/native/libSkiaSharp.so", "tools/manager/runtimes/linux-x64/native/libHarfBuzzSharp.so", "tools/manager/runtimes/linux-x64/native/libSkiaSharp.so", "tools/manager/runtimes/osx/native/libHarfBuzzSharp.dylib", "tools/manager/runtimes/osx/native/libSkiaSharp.dylib", "tools/manager/runtimes/osx/native/libUnoNativeMac.dylib", "tools/manager/runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll", "tools/manager/runtimes/win-arm64/native/libHarfBuzzSharp.dll", "tools/manager/runtimes/win-arm64/native/libSkiaSharp.dll", "tools/manager/runtimes/win-x64/native/libHarfBuzzSharp.dll", "tools/manager/runtimes/win-x64/native/libSkiaSharp.dll", "tools/manager/runtimes/win-x86/native/libHarfBuzzSharp.dll", "tools/manager/runtimes/win-x86/native/libSkiaSharp.dll", "tools/manager/runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "tools/manager/runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll", "tools/manager/splash_screen.png", "tools/manager/splash_screen.scale-100.png", "tools/manager/splash_screen.scale-125.png", "tools/manager/splash_screen.scale-150.png", "tools/manager/splash_screen.scale-200.png", "tools/manager/splash_screen.scale-300.png", "tools/manager/splash_screen.scale-400.png", "tools/manager/tr/Microsoft.CodeAnalysis.resources.dll", "tools/manager/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "tools/manager/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "uno.png", "uno.settings.devserver.1.3.12.nupkg.sha512", "uno.settings.devserver.nuspec"]}, "Uno.Themes.WinUI/5.5.4": {"sha512": "UBJLUMKHztw8VZ0tZnNa6uVfqZH0NUgEFmLB0RwVSbC4tARZdintfx6xKbd49vQThYX4eCIutmLltT5/jWSnag==", "type": "package", "path": "uno.themes.winui/5.5.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "buildTransitive/Uno.Themes.WinUI.targets", "lib/net8.0-android34.0/Uno.Themes.WinUI.aar", "lib/net8.0-android34.0/Uno.Themes.WinUI.dll", "lib/net8.0-android34.0/Uno.Themes.WinUI.pdb", "lib/net8.0-android34.0/Uno.Themes.WinUI.xml", "lib/net8.0-ios18.0/Uno.Themes.WinUI.dll", "lib/net8.0-ios18.0/Uno.Themes.WinUI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Themes.WinUI.dll", "lib/net8.0-maccatalyst18.0/Uno.Themes.WinUI.pdb", "lib/net8.0-macos15.0/Uno.Themes.WinUI.dll", "lib/net8.0-macos15.0/Uno.Themes.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Themes.WinUI.dll", "lib/net8.0-windows10.0.19041/Uno.Themes.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Themes.WinUI.pri", "lib/net8.0/Uno.Themes.WinUI.dll", "lib/net8.0/Uno.Themes.WinUI.pdb", "uno-logo.png", "uno.png", "uno.themes.winui.5.5.4.nupkg.sha512", "uno.themes.winui.nuspec"]}, "Uno.Toolkit/7.0.7": {"sha512": "6VtUs6YGRrDna0YI3x+idmkiB2HAIGgop8/XueG41R1LHpqSEuRKqGgE52DYdUKfvSk9FB6O1jjH2hYuKtrvGA==", "type": "package", "path": "uno.toolkit/7.0.7", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Uno.Toolkit.dll", "lib/netstandard2.0/Uno.Toolkit.pdb", "uno.png", "uno.toolkit.7.0.7.nupkg.sha512", "uno.toolkit.nuspec"]}, "Uno.Toolkit.WinUI/7.0.7": {"sha512": "Jm0zEmyxJEUghQyq9brYxXx9eP7hbM84KKKdp85jbc7TAhXqLRIiTQN+de48JJW1D2ho8nkU6wGlToF8I6bHHg==", "type": "package", "path": "uno.toolkit.winui/7.0.7", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Toolkit.WinUI.targets", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.aar", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.dll", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.pdb", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.xml", "lib/net8.0-ios18.0/Uno.Toolkit.WinUI.dll", "lib/net8.0-ios18.0/Uno.Toolkit.WinUI.pdb", "lib/net8.0-maccatalyst18.0/Uno.Toolkit.WinUI.dll", "lib/net8.0-maccatalyst18.0/Uno.Toolkit.WinUI.pdb", "lib/net8.0-macos15.0/Uno.Toolkit.WinUI.dll", "lib/net8.0-macos15.0/Uno.Toolkit.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Toolkit.WinUI.dll", "lib/net8.0-windows10.0.19041/Uno.Toolkit.WinUI.pdb", "lib/net8.0-windows10.0.19041/Uno.Toolkit.WinUI.pri", "lib/net8.0/Uno.Toolkit.WinUI.dll", "lib/net8.0/Uno.Toolkit.WinUI.pdb", "uno.png", "uno.toolkit.winui.7.0.7.nupkg.sha512", "uno.toolkit.winui.nuspec"]}, "Uno.Toolkit.WinUI.Material/7.0.7": {"sha512": "F2oXcSv1gtodoJXYy23XiRQTfFA6s0Q97CtXqVbIQAtRHaINoNp4ftGO09DQgO77DVgXmezUmrTVkUp2etPwGA==", "type": "package", "path": "uno.toolkit.winui.material/7.0.7", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.Toolkit.WinUI.Material.targets", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.Material.aar", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.Material.dll", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.Material.pdb", "lib/net8.0-android34.0/Uno.Toolkit.WinUI.Material.xml", "lib/net8.0-ios18.0/Uno.Toolkit.WinUI.Material.dll", "lib/net8.0-ios18.0/Uno.Toolkit.WinUI.Material.pdb", "lib/net8.0-maccatalyst18.0/Uno.Toolkit.WinUI.Material.dll", "lib/net8.0-maccatalyst18.0/Uno.Toolkit.WinUI.Material.pdb", "lib/net8.0-macos15.0/Uno.Toolkit.WinUI.Material.dll", "lib/net8.0-macos15.0/Uno.Toolkit.WinUI.Material.pdb", "lib/net8.0-windows10.0.19041/Uno.Toolkit.WinUI.Material.dll", "lib/net8.0-windows10.0.19041/Uno.Toolkit.WinUI.Material.pdb", "lib/net8.0-windows10.0.19041/Uno.Toolkit.WinUI.Material.pri", "lib/net8.0/Uno.Toolkit.WinUI.Material.dll", "lib/net8.0/Uno.Toolkit.WinUI.Material.pdb", "uno.png", "uno.toolkit.winui.material.7.0.7.nupkg.sha512", "uno.toolkit.winui.material.nuspec"]}, "Uno.UI.Adapter.Microsoft.Extensions.Logging/6.0.797": {"sha512": "lqJYtyBtgtaDOccQLnKAe6+v0gGlg/KLO285s/LEejUqFLyRbWS/3rIPPjW7rEJpL1exGiZTYzmJ2LqIRlW82A==", "type": "package", "path": "uno.ui.adapter.microsoft.extensions.logging/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Uno.UI.Adapter.Microsoft.Extensions.Logging.dll", "lib/net8.0/Uno.UI.Adapter.Microsoft.Extensions.Logging.pdb", "lib/net9.0/Uno.UI.Adapter.Microsoft.Extensions.Logging.dll", "lib/net9.0/Uno.UI.Adapter.Microsoft.Extensions.Logging.pdb", "uno.png", "uno.ui.adapter.microsoft.extensions.logging.6.0.797.nupkg.sha512", "uno.ui.adapter.microsoft.extensions.logging.nuspec"]}, "Uno.UI.HotDesign/1.13.6": {"sha512": "xzTHxUpCvFTSxvdwVYGCCDdKn08gZnGP/yLWnD0ly8RwPL7of5Qlw0umK9B+3BKWOyy/F8i3Aa70Rff2P2/IpQ==", "type": "package", "path": "uno.ui.hotdesign/1.13.6", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Uno.UI.HotDesign.SourceGenerators.dll", "build/Uno.UI.HotDesign.props", "lib/net8.0-android34.0/Uno.UI.HotDesign.Abstractions.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Abstractions.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.Client.Core.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Client.Core.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.Client.Core.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.Client.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Client.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.Client.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.CommunityToolkit.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.CommunityToolkit.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.CommunityToolkit.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.Hierarchy.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Hierarchy.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.Hierarchy.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.Messaging.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Messaging.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.PropertyGrid.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.PropertyGrid.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.PropertyGrid.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.Toolbox.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Toolbox.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.Toolbox.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.Interactions.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.Interactions.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.Interactions.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.Interactivity.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.Interactivity.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.Interactivity.xml", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.Xaml.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.aar", "lib/net8.0-android34.0/Uno.UI.HotDesign.dll", "lib/net8.0-android34.0/Uno.UI.HotDesign.pdb", "lib/net8.0-android34.0/Uno.UI.HotDesign.xml", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Abstractions.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Abstractions.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Client.Core.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Client.Core.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Client.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Client.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.CommunityToolkit.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.CommunityToolkit.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Hierarchy.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Hierarchy.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Messaging.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Messaging.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.PropertyGrid.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.PropertyGrid.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Toolbox.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Toolbox.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Xaml.Interactions.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Xaml.Interactions.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Xaml.Interactivity.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Xaml.Interactivity.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Xaml.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.Xaml.pdb", "lib/net8.0-ios18.0/Uno.UI.HotDesign.dll", "lib/net8.0-ios18.0/Uno.UI.HotDesign.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Abstractions.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Abstractions.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Client.Core.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Client.Core.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Client.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Client.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.CommunityToolkit.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.CommunityToolkit.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Hierarchy.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Hierarchy.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Messaging.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Messaging.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.PropertyGrid.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.PropertyGrid.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Toolbox.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Toolbox.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Xaml.Interactions.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Xaml.Interactions.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Xaml.Interactivity.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Xaml.Interactivity.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Xaml.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.Xaml.pdb", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.dll", "lib/net8.0-maccatalyst18.0/Uno.UI.HotDesign.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Abstractions.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Abstractions.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Client.Core.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Client.Core.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Client.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Client.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.CommunityToolkit.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.CommunityToolkit.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Hierarchy.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Hierarchy.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Messaging.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Messaging.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.PropertyGrid.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.PropertyGrid.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Toolbox.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Toolbox.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Xaml.Interactions.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Xaml.Interactions.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Xaml.Interactivity.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Xaml.Interactivity.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Xaml.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.Xaml.pdb", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.dll", "lib/net8.0-windows10.0.19041/Uno.UI.HotDesign.pdb", "lib/net8.0/Uno.UI.HotDesign.Abstractions.dll", "lib/net8.0/Uno.UI.HotDesign.Abstractions.pdb", "lib/net8.0/Uno.UI.HotDesign.Client.Core.dll", "lib/net8.0/Uno.UI.HotDesign.Client.Core.pdb", "lib/net8.0/Uno.UI.HotDesign.Client.dll", "lib/net8.0/Uno.UI.HotDesign.Client.pdb", "lib/net8.0/Uno.UI.HotDesign.CommunityToolkit.dll", "lib/net8.0/Uno.UI.HotDesign.CommunityToolkit.pdb", "lib/net8.0/Uno.UI.HotDesign.Hierarchy.dll", "lib/net8.0/Uno.UI.HotDesign.Hierarchy.pdb", "lib/net8.0/Uno.UI.HotDesign.Messaging.dll", "lib/net8.0/Uno.UI.HotDesign.Messaging.pdb", "lib/net8.0/Uno.UI.HotDesign.PropertyGrid.dll", "lib/net8.0/Uno.UI.HotDesign.PropertyGrid.pdb", "lib/net8.0/Uno.UI.HotDesign.Toolbox.dll", "lib/net8.0/Uno.UI.HotDesign.Toolbox.pdb", "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactions.dll", "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactions.pdb", "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactivity.dll", "lib/net8.0/Uno.UI.HotDesign.Xaml.Interactivity.pdb", "lib/net8.0/Uno.UI.HotDesign.Xaml.dll", "lib/net8.0/Uno.UI.HotDesign.Xaml.pdb", "lib/net8.0/Uno.UI.HotDesign.deps.json", "lib/net8.0/Uno.UI.HotDesign.dll", "lib/net8.0/Uno.UI.HotDesign.pdb", "readme.md", "tools/net8.0/server/CommonServiceLocator.dll", "tools/net8.0/server/Microsoft.ApplicationInsights.dll", "tools/net8.0/server/Microsoft.DotNet.PlatformAbstractions.dll", "tools/net8.0/server/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/net8.0/server/Microsoft.Extensions.DependencyInjection.dll", "tools/net8.0/server/Microsoft.Extensions.Logging.Abstractions.dll", "tools/net8.0/server/Microsoft.Extensions.Logging.dll", "tools/net8.0/server/Microsoft.Extensions.Options.dll", "tools/net8.0/server/Microsoft.Extensions.Primitives.dll", "tools/net8.0/server/Newtonsoft.Json.dll", "tools/net8.0/server/Uno.Core.Extensions.Logging.Singleton.dll", "tools/net8.0/server/Uno.DevTools.Telemetry.dll", "tools/net8.0/server/Uno.Licensing.Sdk.Contracts.dll", "tools/net8.0/server/Uno.UI.HotDesign.Abstractions.dll", "tools/net8.0/server/Uno.UI.HotDesign.Abstractions.pdb", "tools/net8.0/server/Uno.UI.HotDesign.Messaging.dll", "tools/net8.0/server/Uno.UI.HotDesign.Messaging.pdb", "tools/net8.0/server/Uno.UI.HotDesign.Server.deps.json", "tools/net8.0/server/Uno.UI.HotDesign.Server.dll", "tools/net8.0/server/Uno.UI.HotDesign.Server.pdb", "tools/net8.0/server/Uno.UI.HotDesign.Xaml.dll", "tools/net8.0/server/Uno.UI.HotDesign.Xaml.pdb", "tools/net8.0/server/Uno.UI.RemoteControl.Messaging.dll", "tools/net9.0/windows/Assets/Icons/hd_icon.png", "tools/net9.0/windows/Assets/Icons/iconLargeTile.scale-100.png", "tools/net9.0/windows/Assets/Icons/iconLargeTile.scale-125.png", "tools/net9.0/windows/Assets/Icons/iconLargeTile.scale-150.png", "tools/net9.0/windows/Assets/Icons/iconLargeTile.scale-200.png", "tools/net9.0/windows/Assets/Icons/iconLargeTile.scale-400.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-lightunplated_targetsize-16.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-lightunplated_targetsize-24.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-lightunplated_targetsize-256.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-lightunplated_targetsize-32.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-lightunplated_targetsize-48.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-unplated_targetsize-16.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-unplated_targetsize-24.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-unplated_targetsize-256.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-unplated_targetsize-32.png", "tools/net9.0/windows/Assets/Icons/iconLogo.altform-unplated_targetsize-48.png", "tools/net9.0/windows/Assets/Icons/iconLogo.scale-100.png", "tools/net9.0/windows/Assets/Icons/iconLogo.scale-125.png", "tools/net9.0/windows/Assets/Icons/iconLogo.scale-150.png", "tools/net9.0/windows/Assets/Icons/iconLogo.scale-200.png", "tools/net9.0/windows/Assets/Icons/iconLogo.scale-400.png", "tools/net9.0/windows/Assets/Icons/iconLogo.targetsize-16.png", "tools/net9.0/windows/Assets/Icons/iconLogo.targetsize-24.png", "tools/net9.0/windows/Assets/Icons/iconLogo.targetsize-256.png", "tools/net9.0/windows/Assets/Icons/iconLogo.targetsize-32.png", "tools/net9.0/windows/Assets/Icons/iconLogo.targetsize-48.png", "tools/net9.0/windows/Assets/Icons/iconMediumTile.scale-100.png", "tools/net9.0/windows/Assets/Icons/iconMediumTile.scale-125.png", "tools/net9.0/windows/Assets/Icons/iconMediumTile.scale-150.png", "tools/net9.0/windows/Assets/Icons/iconMediumTile.scale-200.png", "tools/net9.0/windows/Assets/Icons/iconMediumTile.scale-400.png", "tools/net9.0/windows/Assets/Icons/iconSmallTile.scale-100.png", "tools/net9.0/windows/Assets/Icons/iconSmallTile.scale-125.png", "tools/net9.0/windows/Assets/Icons/iconSmallTile.scale-150.png", "tools/net9.0/windows/Assets/Icons/iconSmallTile.scale-200.png", "tools/net9.0/windows/Assets/Icons/iconSmallTile.scale-400.png", "tools/net9.0/windows/Assets/Icons/iconStoreLogo.scale-100.png", "tools/net9.0/windows/Assets/Icons/iconStoreLogo.scale-125.png", "tools/net9.0/windows/Assets/Icons/iconStoreLogo.scale-150.png", "tools/net9.0/windows/Assets/Icons/iconStoreLogo.scale-200.png", "tools/net9.0/windows/Assets/Icons/iconStoreLogo.scale-400.png", "tools/net9.0/windows/Assets/Icons/iconWideTile.scale-100.png", "tools/net9.0/windows/Assets/Icons/iconWideTile.scale-125.png", "tools/net9.0/windows/Assets/Icons/iconWideTile.scale-150.png", "tools/net9.0/windows/Assets/Icons/iconWideTile.scale-200.png", "tools/net9.0/windows/Assets/Icons/iconWideTile.scale-400.png", "tools/net9.0/windows/Assets/Icons/icon_foreground.png", "tools/net9.0/windows/Assets/Icons/icon_foreground.scale-100.png", "tools/net9.0/windows/Assets/Icons/icon_foreground.scale-125.png", "tools/net9.0/windows/Assets/Icons/icon_foreground.scale-150.png", "tools/net9.0/windows/Assets/Icons/icon_foreground.scale-200.png", "tools/net9.0/windows/Assets/Icons/icon_foreground.scale-300.png", "tools/net9.0/windows/Assets/Icons/icon_foreground.scale-400.png", "tools/net9.0/windows/Assets/SharedAssets.md", "tools/net9.0/windows/CommonServiceLocator.dll", "tools/net9.0/windows/CommunityToolkit.Mvvm.dll", "tools/net9.0/windows/HarfBuzzSharp.dll", "tools/net9.0/windows/LibVLCSharp.dll", "tools/net9.0/windows/Microsoft.ApplicationInsights.dll", "tools/net9.0/windows/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net9.0/windows/Microsoft.DotNet.PlatformAbstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.Abstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.Binder.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.CommandLine.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.FileExtensions.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.Json.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.UserSecrets.dll", "tools/net9.0/windows/Microsoft.Extensions.Configuration.dll", "tools/net9.0/windows/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.DependencyInjection.dll", "tools/net9.0/windows/Microsoft.Extensions.DependencyModel.dll", "tools/net9.0/windows/Microsoft.Extensions.Diagnostics.Abstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.Diagnostics.dll", "tools/net9.0/windows/Microsoft.Extensions.FileProviders.Abstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.FileProviders.Physical.dll", "tools/net9.0/windows/Microsoft.Extensions.FileSystemGlobbing.dll", "tools/net9.0/windows/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.Hosting.dll", "tools/net9.0/windows/Microsoft.Extensions.Localization.Abstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.Logging.Abstractions.dll", "tools/net9.0/windows/Microsoft.Extensions.Logging.Configuration.dll", "tools/net9.0/windows/Microsoft.Extensions.Logging.Console.dll", "tools/net9.0/windows/Microsoft.Extensions.Logging.Debug.dll", "tools/net9.0/windows/Microsoft.Extensions.Logging.EventLog.dll", "tools/net9.0/windows/Microsoft.Extensions.Logging.EventSource.dll", "tools/net9.0/windows/Microsoft.Extensions.Logging.dll", "tools/net9.0/windows/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "tools/net9.0/windows/Microsoft.Extensions.Options.dll", "tools/net9.0/windows/Microsoft.Extensions.Primitives.dll", "tools/net9.0/windows/Microsoft.Win32.SystemEvents.dll", "tools/net9.0/windows/Newtonsoft.Json.dll", "tools/net9.0/windows/Serilog.Extensions.Hosting.dll", "tools/net9.0/windows/Serilog.Extensions.Logging.dll", "tools/net9.0/windows/Serilog.Settings.Configuration.dll", "tools/net9.0/windows/Serilog.Sinks.Console.dll", "tools/net9.0/windows/Serilog.Sinks.Debug.dll", "tools/net9.0/windows/Serilog.Sinks.File.dll", "tools/net9.0/windows/Serilog.dll", "tools/net9.0/windows/SkiaSharp.Resources.dll", "tools/net9.0/windows/SkiaSharp.SceneGraph.dll", "tools/net9.0/windows/SkiaSharp.Skottie.dll", "tools/net9.0/windows/SkiaSharp.Views.Windows.dll", "tools/net9.0/windows/SkiaSharp.Views.Windows.pdb", "tools/net9.0/windows/SkiaSharp.dll", "tools/net9.0/windows/System.Diagnostics.EventLog.dll", "tools/net9.0/windows/System.Json.dll", "tools/net9.0/windows/Tmds.DBus.Protocol.dll", "tools/net9.0/windows/Uno.Core.Extensions.Collections.dll", "tools/net9.0/windows/Uno.Core.Extensions.Disposables.dll", "tools/net9.0/windows/Uno.Core.Extensions.Equality.dll", "tools/net9.0/windows/Uno.Core.Extensions.Logging.Singleton.dll", "tools/net9.0/windows/Uno.Core.Extensions.Logging.dll", "tools/net9.0/windows/Uno.Core.Extensions.dll", "tools/net9.0/windows/Uno.DevTools.Telemetry.dll", "tools/net9.0/windows/Uno.Diagnostics.Eventing.dll", "tools/net9.0/windows/Uno.Extensions.Configuration.dll", "tools/net9.0/windows/Uno.Extensions.Core.UI.dll", "tools/net9.0/windows/Uno.Extensions.Core.dll", "tools/net9.0/windows/Uno.Extensions.Hosting.WinUI.dll", "tools/net9.0/windows/Uno.Extensions.Hosting.dll", "tools/net9.0/windows/Uno.Extensions.Logging.Serilog.dll", "tools/net9.0/windows/Uno.Extensions.Logging.WebAssembly.Console.dll", "tools/net9.0/windows/Uno.Extensions.Logging.WinUI.dll", "tools/net9.0/windows/Uno.Extensions.Navigation.Toolkit.UI.dll", "tools/net9.0/windows/Uno.Extensions.Navigation.UI.dll", "tools/net9.0/windows/Uno.Extensions.Navigation.dll", "tools/net9.0/windows/Uno.Extensions.Serialization.dll", "tools/net9.0/windows/Uno.Extensions.Storage.UI.dll", "tools/net9.0/windows/Uno.Extensions.Storage.dll", "tools/net9.0/windows/Uno.Fonts.Fluent.dll", "tools/net9.0/windows/Uno.Fonts.Fluent/Fonts/uno-fluentui-assets.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans.dll", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-Bold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-BoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-ExtraBold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-ExtraBoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-Italic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-Light.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-LightItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-Medium.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-MediumItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-Regular.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-SemiBold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans-SemiBoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans.ttf.manifest", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Bold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-BoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-ExtraBold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-ExtraBoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Italic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Light.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-LightItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Medium.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-MediumItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-Regular.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-SemiBold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_Condensed-SemiBoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Bold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-BoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-ExtraBold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-ExtraBoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Italic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Light.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-LightItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Medium.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-MediumItalic.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-Regular.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-SemiBold.ttf", "tools/net9.0/windows/Uno.Fonts.OpenSans/Fonts/OpenSans_SemiCondensed-SemiBoldItalic.ttf", "tools/net9.0/windows/Uno.Fonts.Roboto.dll", "tools/net9.0/windows/Uno.Fonts.Roboto/Fonts/Roboto-Light.ttf", "tools/net9.0/windows/Uno.Fonts.Roboto/Fonts/Roboto-Medium.ttf", "tools/net9.0/windows/Uno.Fonts.Roboto/Fonts/Roboto-Regular.ttf", "tools/net9.0/windows/Uno.Foundation.Logging.dll", "tools/net9.0/windows/Uno.Foundation.dll", "tools/net9.0/windows/Uno.Foundation.pdb", "tools/net9.0/windows/Uno.Licensing.Sdk.Contracts.dll", "tools/net9.0/windows/Uno.Themes.WinUI.dll", "tools/net9.0/windows/Uno.Toolkit.Skia.WinUI.dll", "tools/net9.0/windows/Uno.Toolkit.WinUI.dll", "tools/net9.0/windows/Uno.Toolkit.dll", "tools/net9.0/windows/Uno.UI.Adapter.Microsoft.Extensions.Logging.dll", "tools/net9.0/windows/Uno.UI.Composition.dll", "tools/net9.0/windows/Uno.UI.Composition.pdb", "tools/net9.0/windows/Uno.UI.Dispatching.dll", "tools/net9.0/windows/Uno.UI.Dispatching.pdb", "tools/net9.0/windows/Uno.UI.FluentTheme.dll", "tools/net9.0/windows/Uno.UI.FluentTheme.pdb", "tools/net9.0/windows/Uno.UI.FluentTheme.v1.dll", "tools/net9.0/windows/Uno.UI.FluentTheme.v1.pdb", "tools/net9.0/windows/Uno.UI.FluentTheme.v2.dll", "tools/net9.0/windows/Uno.UI.FluentTheme.v2.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Abstractions.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Abstractions.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Client.Core.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Client.Core.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Client.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Client.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/ExitHotDesignMode.png", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/unologo.png", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/unologo.scale-100.png", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/unologo.scale-125.png", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/unologo.scale-150.png", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/unologo.scale-200.png", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/unologo.scale-300.png", "tools/net9.0/windows/Uno.UI.HotDesign.Client/Assets/unologo.scale-400.png", "tools/net9.0/windows/Uno.UI.HotDesign.CommunityToolkit.dll", "tools/net9.0/windows/Uno.UI.HotDesign.CommunityToolkit.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Hierarchy.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Hierarchy.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Messaging.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Messaging.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.PropertyGrid.dll", "tools/net9.0/windows/Uno.UI.HotDesign.PropertyGrid.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Server.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Server.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Toolbox.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Toolbox.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Windows.deps.json", "tools/net9.0/windows/Uno.UI.HotDesign.Windows.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Windows.exe", "tools/net9.0/windows/Uno.UI.HotDesign.Windows.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Windows.runtimeconfig.json", "tools/net9.0/windows/Uno.UI.HotDesign.Windows.uprimarker", "tools/net9.0/windows/Uno.UI.HotDesign.Xaml.Interactions.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Xaml.Interactions.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Xaml.Interactivity.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Xaml.Interactivity.pdb", "tools/net9.0/windows/Uno.UI.HotDesign.Xaml.dll", "tools/net9.0/windows/Uno.UI.HotDesign.Xaml.pdb", "tools/net9.0/windows/Uno.UI.Lottie.dll", "tools/net9.0/windows/Uno.UI.Lottie.pdb", "tools/net9.0/windows/Uno.UI.RemoteControl.Messaging.dll", "tools/net9.0/windows/Uno.UI.RemoteControl.dll", "tools/net9.0/windows/Uno.UI.RemoteControl.pdb", "tools/net9.0/windows/Uno.UI.Runtime.Skia.Linux.FrameBuffer.dll", "tools/net9.0/windows/Uno.UI.Runtime.Skia.MacOS.dll", "tools/net9.0/windows/Uno.UI.Runtime.Skia.Win32.Support.dll", "tools/net9.0/windows/Uno.UI.Runtime.Skia.Win32.dll", "tools/net9.0/windows/Uno.UI.Runtime.Skia.Wpf.dll", "tools/net9.0/windows/Uno.UI.Runtime.Skia.X11.dll", "tools/net9.0/windows/Uno.UI.Runtime.Skia.dll", "tools/net9.0/windows/Uno.UI.Toolkit.dll", "tools/net9.0/windows/Uno.UI.Toolkit.pdb", "tools/net9.0/windows/Uno.UI.dll", "tools/net9.0/windows/Uno.UI.pdb", "tools/net9.0/windows/Uno.Wasm.WebSockets.dll", "tools/net9.0/windows/Uno.WinUI.Graphics2DSK.dll", "tools/net9.0/windows/Uno.Xaml.dll", "tools/net9.0/windows/Uno.Xaml.pdb", "tools/net9.0/windows/Uno.dll", "tools/net9.0/windows/Uno.pdb", "tools/net9.0/windows/icon.ico", "tools/net9.0/windows/runtimes/linux-arm/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-arm/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-arm64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-arm64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-loongarch64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-loongarch64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-musl-arm/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-musl-arm/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-musl-arm64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-musl-loongarch64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-musl-riscv64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-musl-x64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-riscv64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-riscv64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-x64/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-x64/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/linux-x86/native/libHarfBuzzSharp.so", "tools/net9.0/windows/runtimes/linux-x86/native/libSkiaSharp.so", "tools/net9.0/windows/runtimes/osx/native/libHarfBuzzSharp.dylib", "tools/net9.0/windows/runtimes/osx/native/libSkiaSharp.dylib", "tools/net9.0/windows/runtimes/osx/native/libUnoNativeMac.dylib", "tools/net9.0/windows/runtimes/win-arm64/native/libHarfBuzzSharp.dll", "tools/net9.0/windows/runtimes/win-arm64/native/libSkiaSharp.dll", "tools/net9.0/windows/runtimes/win-x64/native/libHarfBuzzSharp.dll", "tools/net9.0/windows/runtimes/win-x64/native/libSkiaSharp.dll", "tools/net9.0/windows/runtimes/win-x86/native/libHarfBuzzSharp.dll", "tools/net9.0/windows/runtimes/win-x86/native/libSkiaSharp.dll", "tools/net9.0/windows/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "tools/net9.0/windows/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "tools/net9.0/windows/runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll", "tools/net9.0/windows/splash_screen.png", "tools/net9.0/windows/splash_screen.scale-100.png", "tools/net9.0/windows/splash_screen.scale-125.png", "tools/net9.0/windows/splash_screen.scale-150.png", "tools/net9.0/windows/splash_screen.scale-200.png", "tools/net9.0/windows/splash_screen.scale-300.png", "tools/net9.0/windows/splash_screen.scale-400.png", "uno.png", "uno.ui.hotdesign.1.13.6.nupkg.sha512", "uno.ui.hotdesign.nuspec"]}, "Uno.Wasm.WebSockets/1.1.0": {"sha512": "U2d297Z/CxP0GZ3mYCu8kdKzO7PKw5lE7j+gwgXAZS1OlZiWS9EoiBbpUmr/0nSH6wPqClMbtGi6LLs0jslOog==", "type": "package", "path": "uno.wasm.websockets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Uno.Wasm.WebSockets.dll", "lib/netstandard2.0/Uno.Wasm.WebSockets.pdb", "uno.wasm.websockets.1.1.0.nupkg.sha512", "uno.wasm.websockets.nuspec"]}, "Uno.WinRT/6.0.797": {"sha512": "QZYUdcTZb3qcJnDsFCH/lifi9V5STOsTegX+WAbxV3kMi9mlXYlgUwesa61BGQ0LNoRplYY3xEGafF3dBGQZNQ==", "type": "package", "path": "uno.winrt/6.0.797", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.WinRT.props", "lib/net8.0-android30.0/Uno.UI.Dispatching.dll", "lib/net8.0-android30.0/Uno.UI.Dispatching.pdb", "lib/net8.0-android30.0/Uno.dll", "lib/net8.0-android30.0/Uno.pdb", "lib/net8.0-ios17.0/Uno.UI.Dispatching.dll", "lib/net8.0-ios17.0/Uno.UI.Dispatching.pdb", "lib/net8.0-ios17.0/Uno.dll", "lib/net8.0-ios17.0/Uno.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.Dispatching.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.Dispatching.pdb", "lib/net8.0-maccatalyst17.0/Uno.dll", "lib/net8.0-maccatalyst17.0/Uno.pdb", "lib/net8.0-tvos17.0/Uno.UI.Dispatching.dll", "lib/net8.0-tvos17.0/Uno.UI.Dispatching.pdb", "lib/net8.0-tvos17.0/Uno.dll", "lib/net8.0-tvos17.0/Uno.pdb", "lib/net8.0/Uno.UI.Dispatching.dll", "lib/net8.0/Uno.dll", "lib/net9.0-android30.0/Uno.UI.Dispatching.dll", "lib/net9.0-android30.0/Uno.UI.Dispatching.pdb", "lib/net9.0-android30.0/Uno.dll", "lib/net9.0-android30.0/Uno.pdb", "lib/net9.0-ios18.0/Uno.UI.Dispatching.dll", "lib/net9.0-ios18.0/Uno.UI.Dispatching.pdb", "lib/net9.0-ios18.0/Uno.dll", "lib/net9.0-ios18.0/Uno.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.Dispatching.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.Dispatching.pdb", "lib/net9.0-maccatalyst18.0/Uno.dll", "lib/net9.0-maccatalyst18.0/Uno.pdb", "lib/net9.0-tvos18.0/Uno.UI.Dispatching.dll", "lib/net9.0-tvos18.0/Uno.UI.Dispatching.pdb", "lib/net9.0-tvos18.0/Uno.dll", "lib/net9.0-tvos18.0/Uno.pdb", "lib/net9.0/Uno.UI.Dispatching.dll", "lib/net9.0/Uno.dll", "tools/_._", "uno-runtime/net8.0/skia/Uno.UI.Dispatching.dll", "uno-runtime/net8.0/skia/Uno.UI.Dispatching.pdb", "uno-runtime/net8.0/skia/Uno.dll", "uno-runtime/net8.0/skia/Uno.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.Dispatching.dll", "uno-runtime/net8.0/webassembly/Uno.UI.Dispatching.pdb", "uno-runtime/net8.0/webassembly/Uno.dll", "uno-runtime/net8.0/webassembly/Uno.pdb", "uno-runtime/net9.0/skia/Uno.UI.Dispatching.dll", "uno-runtime/net9.0/skia/Uno.UI.Dispatching.pdb", "uno-runtime/net9.0/skia/Uno.dll", "uno-runtime/net9.0/skia/Uno.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.Dispatching.dll", "uno-runtime/net9.0/webassembly/Uno.UI.Dispatching.pdb", "uno-runtime/net9.0/webassembly/Uno.dll", "uno-runtime/net9.0/webassembly/Uno.pdb", "uno.png", "uno.winrt.6.0.797.nupkg.sha512", "uno.winrt.nuspec"]}, "Uno.WinUI/6.0.797": {"sha512": "LPt346D8gWVazXzanIxKgG5oSnyr2xsPRXtC2npAwPivkLwDOrkU1+zqqJWKfXTpWMJ2EAeykBOzdP4expXc7A==", "type": "package", "path": "uno.winui/6.0.797", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Microsoft.ApplicationInsights.dll", "analyzers/dotnet/cs/Microsoft.DotNet.PlatformAbstractions.dll", "analyzers/dotnet/cs/System.Diagnostics.DiagnosticSource.dll", "analyzers/dotnet/cs/Uno.Analyzers.dll", "analyzers/dotnet/cs/Uno.Analyzers.pdb", "analyzers/dotnet/cs/Uno.DevTools.Telemetry.dll", "analyzers/dotnet/cs/Uno.UI.SourceGenerators.dll", "analyzers/dotnet/cs/Uno.UI.SourceGenerators.pdb", "analyzers/dotnet/cs/Uno.Xaml.dll", "analyzers/dotnet/cs/Uno.Xaml.pdb", "buildTransitive/Descriptors/Uno.UI.LinkAttributes.Android.xml", "buildTransitive/Uno.UI.SourceGenerators.props", "buildTransitive/Uno.UI.Tasks.targets", "buildTransitive/Uno.UI.Tasks/Mono.Cecil.Mdb.dll", "buildTransitive/Uno.UI.Tasks/Mono.Cecil.Pdb.dll", "buildTransitive/Uno.UI.Tasks/Mono.Cecil.Rocks.dll", "buildTransitive/Uno.UI.Tasks/Mono.Cecil.dll", "buildTransitive/Uno.UI.Tasks/Uno.UI.Tasks.4c6fc11514a03febb97dd23a14beac2d6d1cda53.dll", "buildTransitive/Uno.UI.Tasks/Uno.UI.Tasks.4c6fc11514a03febb97dd23a14beac2d6d1cda53.pdb", "buildTransitive/net8.0-android30.0/uno.winui.props", "buildTransitive/net8.0-android30.0/uno.winui.targets", "buildTransitive/net8.0-ios17.0/uno.winui.props", "buildTransitive/net8.0-ios17.0/uno.winui.targets", "buildTransitive/net8.0-maccatalyst17.0/uno.winui.props", "buildTransitive/net8.0-maccatalyst17.0/uno.winui.targets", "buildTransitive/net8.0-tvos17.0/uno.winui.props", "buildTransitive/net8.0-tvos17.0/uno.winui.targets", "buildTransitive/net8.0/uno.winui.props", "buildTransitive/net8.0/uno.winui.targets", "buildTransitive/net9.0-android30.0/uno.winui.props", "buildTransitive/net9.0-android30.0/uno.winui.targets", "buildTransitive/net9.0-ios18.0/uno.winui.props", "buildTransitive/net9.0-ios18.0/uno.winui.targets", "buildTransitive/net9.0-maccatalyst18.0/uno.winui.props", "buildTransitive/net9.0-maccatalyst18.0/uno.winui.targets", "buildTransitive/net9.0-tvos18.0/uno.winui.props", "buildTransitive/net9.0-tvos18.0/uno.winui.targets", "buildTransitive/net9.0-windows/uno.winui.props", "buildTransitive/net9.0-windows/uno.winui.targets", "buildTransitive/uap10.0.16299/uno.winui.runtime-replace.targets", "buildTransitive/uap10.0.16299/uno.winui.targets", "buildTransitive/uap10.0.17763/uno.winui.runtime-replace.targets", "buildTransitive/uap10.0.17763/uno.winui.targets", "buildTransitive/uap10.0.19041/uno.winui.runtime-replace.targets", "buildTransitive/uap10.0.19041/uno.winui.targets", "buildTransitive/uno.ui.tasks.assets.targets", "buildTransitive/uno.winui.common.props", "buildTransitive/uno.winui.common.targets", "buildTransitive/uno.winui.cross-runtime.targets", "buildTransitive/uno.winui.runtime-replace.targets", "buildTransitive/uno.winui.single-project.targets", "buildTransitive/uno.winui.winappsdk.targets", "lib/net8.0-android30.0/Uno.UI.BindingHelper.Android.aar", "lib/net8.0-android30.0/Uno.UI.BindingHelper.Android.dll", "lib/net8.0-android30.0/Uno.UI.BindingHelper.Android.pdb", "lib/net8.0-android30.0/Uno.UI.Composition.dll", "lib/net8.0-android30.0/Uno.UI.Composition.pdb", "lib/net8.0-android30.0/Uno.UI.FluentTheme.dll", "lib/net8.0-android30.0/Uno.UI.FluentTheme.pdb", "lib/net8.0-android30.0/Uno.UI.FluentTheme.v1.dll", "lib/net8.0-android30.0/Uno.UI.FluentTheme.v1.pdb", "lib/net8.0-android30.0/Uno.UI.FluentTheme.v2.dll", "lib/net8.0-android30.0/Uno.UI.FluentTheme.v2.pdb", "lib/net8.0-android30.0/Uno.UI.Toolkit.dll", "lib/net8.0-android30.0/Uno.UI.Toolkit.pdb", "lib/net8.0-android30.0/Uno.UI.dll", "lib/net8.0-android30.0/Uno.UI.pdb", "lib/net8.0-android30.0/Uno.UI.xml", "lib/net8.0-android30.0/Uno.Xaml.dll", "lib/net8.0-android30.0/Uno.Xaml.pdb", "lib/net8.0-ios17.0/Uno.UI.Composition.dll", "lib/net8.0-ios17.0/Uno.UI.Composition.pdb", "lib/net8.0-ios17.0/Uno.UI.FluentTheme.dll", "lib/net8.0-ios17.0/Uno.UI.FluentTheme.pdb", "lib/net8.0-ios17.0/Uno.UI.FluentTheme.v1.dll", "lib/net8.0-ios17.0/Uno.UI.FluentTheme.v1.pdb", "lib/net8.0-ios17.0/Uno.UI.FluentTheme.v2.dll", "lib/net8.0-ios17.0/Uno.UI.FluentTheme.v2.pdb", "lib/net8.0-ios17.0/Uno.UI.Toolkit.dll", "lib/net8.0-ios17.0/Uno.UI.Toolkit.pdb", "lib/net8.0-ios17.0/Uno.UI.dll", "lib/net8.0-ios17.0/Uno.UI.pdb", "lib/net8.0-ios17.0/Uno.Xaml.dll", "lib/net8.0-ios17.0/Uno.Xaml.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.Composition.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.Composition.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.FluentTheme.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.FluentTheme.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.FluentTheme.v1.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.FluentTheme.v1.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.FluentTheme.v2.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.FluentTheme.v2.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.Toolkit.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.Toolkit.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.pdb", "lib/net8.0-maccatalyst17.0/Uno.Xaml.dll", "lib/net8.0-maccatalyst17.0/Uno.Xaml.pdb", "lib/net8.0-tvos17.0/Uno.UI.Composition.dll", "lib/net8.0-tvos17.0/Uno.UI.Composition.pdb", "lib/net8.0-tvos17.0/Uno.UI.FluentTheme.dll", "lib/net8.0-tvos17.0/Uno.UI.FluentTheme.pdb", "lib/net8.0-tvos17.0/Uno.UI.FluentTheme.v1.dll", "lib/net8.0-tvos17.0/Uno.UI.FluentTheme.v1.pdb", "lib/net8.0-tvos17.0/Uno.UI.FluentTheme.v2.dll", "lib/net8.0-tvos17.0/Uno.UI.FluentTheme.v2.pdb", "lib/net8.0-tvos17.0/Uno.UI.Toolkit.dll", "lib/net8.0-tvos17.0/Uno.UI.Toolkit.pdb", "lib/net8.0-tvos17.0/Uno.UI.dll", "lib/net8.0-tvos17.0/Uno.UI.pdb", "lib/net8.0-tvos17.0/Uno.Xaml.dll", "lib/net8.0-tvos17.0/Uno.Xaml.pdb", "lib/net8.0-windows10.0.19041.0/Uno.UI.Toolkit.dll", "lib/net8.0-windows10.0.19041.0/Uno.UI.Toolkit.pdb", "lib/net8.0-windows10.0.19041.0/Uno.UI.Toolkit.pri", "lib/net8.0-windows10.0.19041.0/Uno.UI.Toolkit/Themes/Generic.xaml", "lib/net8.0-windows10.0.19041.0/Uno.UI.Toolkit/Themes/Generic.xbf", "lib/net8.0/Uno.UI.Composition.dll", "lib/net8.0/Uno.UI.FluentTheme.dll", "lib/net8.0/Uno.UI.FluentTheme.pdb", "lib/net8.0/Uno.UI.FluentTheme.v1.dll", "lib/net8.0/Uno.UI.FluentTheme.v1.pdb", "lib/net8.0/Uno.UI.FluentTheme.v2.dll", "lib/net8.0/Uno.UI.FluentTheme.v2.pdb", "lib/net8.0/Uno.UI.Toolkit.dll", "lib/net8.0/Uno.UI.Toolkit.pdb", "lib/net8.0/Uno.UI.dll", "lib/net8.0/Uno.Xaml.dll", "lib/net8.0/Uno.Xaml.pdb", "lib/net9.0-android30.0/Uno.UI.BindingHelper.Android.aar", "lib/net9.0-android30.0/Uno.UI.BindingHelper.Android.dll", "lib/net9.0-android30.0/Uno.UI.BindingHelper.Android.pdb", "lib/net9.0-android30.0/Uno.UI.Composition.dll", "lib/net9.0-android30.0/Uno.UI.Composition.pdb", "lib/net9.0-android30.0/Uno.UI.FluentTheme.dll", "lib/net9.0-android30.0/Uno.UI.FluentTheme.pdb", "lib/net9.0-android30.0/Uno.UI.FluentTheme.v1.dll", "lib/net9.0-android30.0/Uno.UI.FluentTheme.v1.pdb", "lib/net9.0-android30.0/Uno.UI.FluentTheme.v2.dll", "lib/net9.0-android30.0/Uno.UI.FluentTheme.v2.pdb", "lib/net9.0-android30.0/Uno.UI.Toolkit.dll", "lib/net9.0-android30.0/Uno.UI.Toolkit.pdb", "lib/net9.0-android30.0/Uno.UI.dll", "lib/net9.0-android30.0/Uno.UI.pdb", "lib/net9.0-android30.0/Uno.UI.xml", "lib/net9.0-android30.0/Uno.Xaml.dll", "lib/net9.0-android30.0/Uno.Xaml.pdb", "lib/net9.0-ios18.0/Uno.UI.Composition.dll", "lib/net9.0-ios18.0/Uno.UI.Composition.pdb", "lib/net9.0-ios18.0/Uno.UI.FluentTheme.dll", "lib/net9.0-ios18.0/Uno.UI.FluentTheme.pdb", "lib/net9.0-ios18.0/Uno.UI.FluentTheme.v1.dll", "lib/net9.0-ios18.0/Uno.UI.FluentTheme.v1.pdb", "lib/net9.0-ios18.0/Uno.UI.FluentTheme.v2.dll", "lib/net9.0-ios18.0/Uno.UI.FluentTheme.v2.pdb", "lib/net9.0-ios18.0/Uno.UI.Toolkit.dll", "lib/net9.0-ios18.0/Uno.UI.Toolkit.pdb", "lib/net9.0-ios18.0/Uno.UI.dll", "lib/net9.0-ios18.0/Uno.UI.pdb", "lib/net9.0-ios18.0/Uno.Xaml.dll", "lib/net9.0-ios18.0/Uno.Xaml.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.Composition.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.Composition.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.FluentTheme.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.FluentTheme.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.FluentTheme.v1.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.FluentTheme.v1.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.FluentTheme.v2.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.FluentTheme.v2.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.Toolkit.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.Toolkit.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.pdb", "lib/net9.0-maccatalyst18.0/Uno.Xaml.dll", "lib/net9.0-maccatalyst18.0/Uno.Xaml.pdb", "lib/net9.0-tvos18.0/Uno.UI.Composition.dll", "lib/net9.0-tvos18.0/Uno.UI.Composition.pdb", "lib/net9.0-tvos18.0/Uno.UI.FluentTheme.dll", "lib/net9.0-tvos18.0/Uno.UI.FluentTheme.pdb", "lib/net9.0-tvos18.0/Uno.UI.FluentTheme.v1.dll", "lib/net9.0-tvos18.0/Uno.UI.FluentTheme.v1.pdb", "lib/net9.0-tvos18.0/Uno.UI.FluentTheme.v2.dll", "lib/net9.0-tvos18.0/Uno.UI.FluentTheme.v2.pdb", "lib/net9.0-tvos18.0/Uno.UI.Toolkit.dll", "lib/net9.0-tvos18.0/Uno.UI.Toolkit.pdb", "lib/net9.0-tvos18.0/Uno.UI.dll", "lib/net9.0-tvos18.0/Uno.UI.pdb", "lib/net9.0-tvos18.0/Uno.Xaml.dll", "lib/net9.0-tvos18.0/Uno.Xaml.pdb", "lib/net9.0-windows10.0.19041.0/Uno.UI.Toolkit.dll", "lib/net9.0-windows10.0.19041.0/Uno.UI.Toolkit.pdb", "lib/net9.0-windows10.0.19041.0/Uno.UI.Toolkit.pri", "lib/net9.0-windows10.0.19041.0/Uno.UI.Toolkit/Themes/Generic.xaml", "lib/net9.0-windows10.0.19041.0/Uno.UI.Toolkit/Themes/Generic.xbf", "lib/net9.0/Uno.UI.Composition.dll", "lib/net9.0/Uno.UI.FluentTheme.dll", "lib/net9.0/Uno.UI.FluentTheme.pdb", "lib/net9.0/Uno.UI.FluentTheme.v1.dll", "lib/net9.0/Uno.UI.FluentTheme.v1.pdb", "lib/net9.0/Uno.UI.FluentTheme.v2.dll", "lib/net9.0/Uno.UI.FluentTheme.v2.pdb", "lib/net9.0/Uno.UI.Toolkit.dll", "lib/net9.0/Uno.UI.Toolkit.pdb", "lib/net9.0/Uno.UI.dll", "lib/net9.0/Uno.Xaml.dll", "lib/net9.0/Uno.Xaml.pdb", "tools/_._", "uno-runtime/net8.0/skia/Uno.UI.Composition.dll", "uno-runtime/net8.0/skia/Uno.UI.Composition.pdb", "uno-runtime/net8.0/skia/Uno.UI.FluentTheme.dll", "uno-runtime/net8.0/skia/Uno.UI.FluentTheme.pdb", "uno-runtime/net8.0/skia/Uno.UI.FluentTheme.v1.dll", "uno-runtime/net8.0/skia/Uno.UI.FluentTheme.v1.pdb", "uno-runtime/net8.0/skia/Uno.UI.FluentTheme.v2.dll", "uno-runtime/net8.0/skia/Uno.UI.FluentTheme.v2.pdb", "uno-runtime/net8.0/skia/Uno.UI.Toolkit.dll", "uno-runtime/net8.0/skia/Uno.UI.Toolkit.pdb", "uno-runtime/net8.0/skia/Uno.UI.dll", "uno-runtime/net8.0/skia/Uno.UI.pdb", "uno-runtime/net8.0/skia/Uno.Xaml.dll", "uno-runtime/net8.0/skia/Uno.Xaml.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.Composition.dll", "uno-runtime/net8.0/webassembly/Uno.UI.Composition.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.FluentTheme.dll", "uno-runtime/net8.0/webassembly/Uno.UI.FluentTheme.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.FluentTheme.v1.dll", "uno-runtime/net8.0/webassembly/Uno.UI.FluentTheme.v1.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.FluentTheme.v2.dll", "uno-runtime/net8.0/webassembly/Uno.UI.FluentTheme.v2.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.Toolkit.dll", "uno-runtime/net8.0/webassembly/Uno.UI.Toolkit.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.dll", "uno-runtime/net8.0/webassembly/Uno.UI.pdb", "uno-runtime/net8.0/webassembly/Uno.Xaml.dll", "uno-runtime/net8.0/webassembly/Uno.Xaml.pdb", "uno-runtime/net9.0/skia/Uno.UI.Composition.dll", "uno-runtime/net9.0/skia/Uno.UI.Composition.pdb", "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.dll", "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.pdb", "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.v1.dll", "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.v1.pdb", "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.v2.dll", "uno-runtime/net9.0/skia/Uno.UI.FluentTheme.v2.pdb", "uno-runtime/net9.0/skia/Uno.UI.Toolkit.dll", "uno-runtime/net9.0/skia/Uno.UI.Toolkit.pdb", "uno-runtime/net9.0/skia/Uno.UI.dll", "uno-runtime/net9.0/skia/Uno.UI.pdb", "uno-runtime/net9.0/skia/Uno.Xaml.dll", "uno-runtime/net9.0/skia/Uno.Xaml.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.Composition.dll", "uno-runtime/net9.0/webassembly/Uno.UI.Composition.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.FluentTheme.dll", "uno-runtime/net9.0/webassembly/Uno.UI.FluentTheme.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.FluentTheme.v1.dll", "uno-runtime/net9.0/webassembly/Uno.UI.FluentTheme.v1.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.FluentTheme.v2.dll", "uno-runtime/net9.0/webassembly/Uno.UI.FluentTheme.v2.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.Toolkit.dll", "uno-runtime/net9.0/webassembly/Uno.UI.Toolkit.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.dll", "uno-runtime/net9.0/webassembly/Uno.UI.pdb", "uno-runtime/net9.0/webassembly/Uno.Xaml.dll", "uno-runtime/net9.0/webassembly/Uno.Xaml.pdb", "uno.png", "uno.winui.6.0.797.nupkg.sha512", "uno.winui.nuspec"]}, "Uno.WinUI.DevServer/6.0.797": {"sha512": "q9pQIAwdo6JQdIOR5b3ZzDoisYbZyKc7qQUZvG24Q+388zSOfnYmU3tYyeb4owTt5NF2EMgqNBPPxDgUKBiT8Q==", "type": "package", "path": "uno.winui.devserver/6.0.797", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/_._", "build/net8.0-windows10.0.19041.0/_._", "build/net9.0-windows10.0.19041.0/_._", "build/uap10.0.16299/_._", "build/uap10.0.19041/_._", "buildTransitive/Uno.WinUI.DevServer.props", "buildTransitive/Uno.WinUI.DevServer.targets", "lib/net8.0-android/Uno.UI.RemoteControl.dll", "lib/net8.0-android/Uno.UI.RemoteControl.pdb", "lib/net8.0-ios17.0/Uno.UI.RemoteControl.dll", "lib/net8.0-ios17.0/Uno.UI.RemoteControl.pdb", "lib/net8.0-maccatalyst17.0/Uno.UI.RemoteControl.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.RemoteControl.pdb", "lib/net8.0-tvos17.0/Uno.UI.RemoteControl.dll", "lib/net8.0-tvos17.0/Uno.UI.RemoteControl.pdb", "lib/net8.0-windows10.0.19041.0/_._", "lib/net8.0/Uno.UI.RemoteControl.dll", "lib/net8.0/Uno.UI.RemoteControl.pdb", "lib/net9.0-android/Uno.UI.RemoteControl.dll", "lib/net9.0-android/Uno.UI.RemoteControl.pdb", "lib/net9.0-ios18.0/Uno.UI.RemoteControl.dll", "lib/net9.0-ios18.0/Uno.UI.RemoteControl.pdb", "lib/net9.0-maccatalyst18.0/Uno.UI.RemoteControl.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.RemoteControl.pdb", "lib/net9.0-tvos18.0/Uno.UI.RemoteControl.dll", "lib/net9.0-tvos18.0/Uno.UI.RemoteControl.pdb", "lib/net9.0-windows10.0.19041.0/_._", "lib/net9.0/Uno.UI.RemoteControl.dll", "lib/net9.0/Uno.UI.RemoteControl.pdb", "tools/rc/17.0/MessagePack.Annotations.dll", "tools/rc/17.0/MessagePack.dll", "tools/rc/17.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/rc/17.0/Microsoft.Build.Framework.dll", "tools/rc/17.0/Microsoft.Build.Tasks.Core.dll", "tools/rc/17.0/Microsoft.Build.Utilities.Core.dll", "tools/rc/17.0/Microsoft.Build.dll", "tools/rc/17.0/Microsoft.IO.Redist.dll", "tools/rc/17.0/Microsoft.NET.StringTools.dll", "tools/rc/17.0/Microsoft.VisualStudio.Composition.NetFxAttributes.dll", "tools/rc/17.0/Microsoft.VisualStudio.Composition.dll", "tools/rc/17.0/Microsoft.VisualStudio.Interop.dll", "tools/rc/17.0/Microsoft.VisualStudio.ProjectSystem.Managed.dll", "tools/rc/17.0/Microsoft.VisualStudio.ProjectSystem.VS.dll", "tools/rc/17.0/Microsoft.VisualStudio.ProjectSystem.dll", "tools/rc/17.0/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "tools/rc/17.0/Microsoft.VisualStudio.Shell.Interop.12.0.dll", "tools/rc/17.0/Microsoft.VisualStudio.TemplateWizardInterface.dll", "tools/rc/17.0/Microsoft.VisualStudio.Threading.dll", "tools/rc/17.0/Microsoft.VisualStudio.Validation.dll", "tools/rc/17.0/Microsoft.Win32.Registry.dll", "tools/rc/17.0/Nerdbank.Streams.dll", "tools/rc/17.0/Newtonsoft.Json.dll", "tools/rc/17.0/NuGet.VisualStudio.dll", "tools/rc/17.0/StreamJsonRpc.dll", "tools/rc/17.0/System.Buffers.dll", "tools/rc/17.0/System.Collections.Immutable.dll", "tools/rc/17.0/System.Composition.AttributedModel.dll", "tools/rc/17.0/System.Composition.Convention.dll", "tools/rc/17.0/System.Composition.Hosting.dll", "tools/rc/17.0/System.Composition.Runtime.dll", "tools/rc/17.0/System.Composition.TypedParts.dll", "tools/rc/17.0/System.Configuration.ConfigurationManager.dll", "tools/rc/17.0/System.Diagnostics.DiagnosticSource.dll", "tools/rc/17.0/System.IO.Pipelines.dll", "tools/rc/17.0/System.Memory.dll", "tools/rc/17.0/System.Numerics.Vectors.dll", "tools/rc/17.0/System.Reflection.Metadata.dll", "tools/rc/17.0/System.Reflection.TypeExtensions.dll", "tools/rc/17.0/System.Resources.Extensions.dll", "tools/rc/17.0/System.Runtime.CompilerServices.Unsafe.dll", "tools/rc/17.0/System.Security.AccessControl.dll", "tools/rc/17.0/System.Security.Permissions.dll", "tools/rc/17.0/System.Security.Principal.Windows.dll", "tools/rc/17.0/System.Text.Encodings.Web.dll", "tools/rc/17.0/System.Text.Json.dll", "tools/rc/17.0/System.Threading.Tasks.Dataflow.dll", "tools/rc/17.0/System.Threading.Tasks.Extensions.dll", "tools/rc/17.0/System.ValueTuple.dll", "tools/rc/17.0/Uno.UI.RemoteControl.Messaging.dll", "tools/rc/17.0/Uno.UI.RemoteControl.Messaging.pdb", "tools/rc/17.0/Uno.UI.RemoteControl.VS.dll", "tools/rc/17.0/Uno.UI.RemoteControl.VS.pdb", "tools/rc/17.0/envdte.dll", "tools/rc/17.0/envdte80.dll", "tools/rc/17.0/stdole.dll", "tools/rc/host/net8.0/CommonServiceLocator.dll", "tools/rc/host/net8.0/DevServer.Custom.Impl.Targets", "tools/rc/host/net8.0/DevServer.Custom.Targets", "tools/rc/host/net8.0/MessagePack.Annotations.dll", "tools/rc/host/net8.0/MessagePack.dll", "tools/rc/host/net8.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/rc/host/net8.0/Microsoft.IO.RecyclableMemoryStream.dll", "tools/rc/host/net8.0/Microsoft.NET.StringTools.dll", "tools/rc/host/net8.0/Microsoft.VisualStudio.Threading.dll", "tools/rc/host/net8.0/Microsoft.VisualStudio.Validation.dll", "tools/rc/host/net8.0/Mono.Options.dll", "tools/rc/host/net8.0/Nerdbank.Streams.dll", "tools/rc/host/net8.0/Newtonsoft.Json.dll", "tools/rc/host/net8.0/StreamJsonRpc.dll", "tools/rc/host/net8.0/System.Reactive.dll", "tools/rc/host/net8.0/Unity.Abstractions.dll", "tools/rc/host/net8.0/Unity.Container.dll", "tools/rc/host/net8.0/Uno.Core.Extensions.Logging.Singleton.dll", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Host.deps.json", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Host.dll", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Host.dll.config", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Host.exe", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Host.pdb", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Host.runtimeconfig.json", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Host.staticwebassets.endpoints.json", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Messaging.dll", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Messaging.pdb", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Server.dll", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Server.dll.config", "tools/rc/host/net8.0/Uno.UI.RemoteControl.Server.pdb", "tools/rc/host/net8.0/global.json", "tools/rc/host/net9.0/CommonServiceLocator.dll", "tools/rc/host/net9.0/DevServer.Custom.Impl.Targets", "tools/rc/host/net9.0/DevServer.Custom.Targets", "tools/rc/host/net9.0/MessagePack.Annotations.dll", "tools/rc/host/net9.0/MessagePack.dll", "tools/rc/host/net9.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/rc/host/net9.0/Microsoft.IO.RecyclableMemoryStream.dll", "tools/rc/host/net9.0/Microsoft.NET.StringTools.dll", "tools/rc/host/net9.0/Microsoft.VisualStudio.Threading.dll", "tools/rc/host/net9.0/Microsoft.VisualStudio.Validation.dll", "tools/rc/host/net9.0/Mono.Options.dll", "tools/rc/host/net9.0/Nerdbank.Streams.dll", "tools/rc/host/net9.0/Newtonsoft.Json.dll", "tools/rc/host/net9.0/StreamJsonRpc.dll", "tools/rc/host/net9.0/System.Reactive.dll", "tools/rc/host/net9.0/Unity.Abstractions.dll", "tools/rc/host/net9.0/Unity.Container.dll", "tools/rc/host/net9.0/Uno.Core.Extensions.Logging.Singleton.dll", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Host.deps.json", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Host.dll", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Host.dll.config", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Host.exe", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Host.pdb", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Host.runtimeconfig.json", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Host.staticwebassets.endpoints.json", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Messaging.dll", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Messaging.pdb", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Server.dll", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Server.dll.config", "tools/rc/host/net9.0/Uno.UI.RemoteControl.Server.pdb", "tools/rc/host/net9.0/global.json", "tools/rc/processors/net8.0/CommonServiceLocator.dll", "tools/rc/processors/net8.0/Humanizer.dll", "tools/rc/processors/net8.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.CSharp.Features.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.CSharp.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.Elfie.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.Features.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.Scripting.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.VisualBasic.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.Workspaces.dll", "tools/rc/processors/net8.0/Microsoft.CodeAnalysis.dll", "tools/rc/processors/net8.0/Microsoft.DiaSymReader.dll", "tools/rc/processors/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/rc/processors/net8.0/Microsoft.Extensions.DependencyInjection.dll", "tools/rc/processors/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "tools/rc/processors/net8.0/Microsoft.Extensions.Logging.dll", "tools/rc/processors/net8.0/Microsoft.Extensions.Options.dll", "tools/rc/processors/net8.0/Microsoft.Extensions.Primitives.dll", "tools/rc/processors/net8.0/Microsoft.Win32.SystemEvents.dll", "tools/rc/processors/net8.0/Newtonsoft.Json.dll", "tools/rc/processors/net8.0/System.Composition.AttributedModel.dll", "tools/rc/processors/net8.0/System.Composition.Convention.dll", "tools/rc/processors/net8.0/System.Composition.Hosting.dll", "tools/rc/processors/net8.0/System.Composition.Runtime.dll", "tools/rc/processors/net8.0/System.Composition.TypedParts.dll", "tools/rc/processors/net8.0/System.Configuration.ConfigurationManager.dll", "tools/rc/processors/net8.0/System.Diagnostics.EventLog.dll", "tools/rc/processors/net8.0/System.Drawing.Common.dll", "tools/rc/processors/net8.0/System.IO.Pipelines.dll", "tools/rc/processors/net8.0/System.Reactive.dll", "tools/rc/processors/net8.0/System.Security.Cryptography.Pkcs.dll", "tools/rc/processors/net8.0/System.Security.Cryptography.ProtectedData.dll", "tools/rc/processors/net8.0/System.Security.Cryptography.Xml.dll", "tools/rc/processors/net8.0/System.Security.Permissions.dll", "tools/rc/processors/net8.0/System.Windows.Extensions.dll", "tools/rc/processors/net8.0/Uno.Core.Extensions.Disposables.dll", "tools/rc/processors/net8.0/Uno.Core.Extensions.Logging.Singleton.dll", "tools/rc/processors/net8.0/Uno.Core.Extensions.dll", "tools/rc/processors/net8.0/Uno.UI.RemoteControl.Messaging.dll", "tools/rc/processors/net8.0/Uno.UI.RemoteControl.Messaging.pdb", "tools/rc/processors/net8.0/Uno.UI.RemoteControl.Server.Processors.dll", "tools/rc/processors/net8.0/Uno.UI.RemoteControl.Server.Processors.pdb", "tools/rc/processors/net8.0/Uno.UI.RemoteControl.Server.dll", "tools/rc/processors/net8.0/Uno.UI.RemoteControl.Server.pdb", "tools/rc/processors/net9.0/BuildHost-netcore/Microsoft.Build.Locator.dll", "tools/rc/processors/net9.0/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json", "tools/rc/processors/net9.0/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll", "tools/rc/processors/net9.0/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config", "tools/rc/processors/net9.0/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json", "tools/rc/processors/net9.0/BuildHost-netcore/Newtonsoft.Json.dll", "tools/rc/processors/net9.0/BuildHost-netcore/System.Collections.Immutable.dll", "tools/rc/processors/net9.0/BuildHost-netcore/System.CommandLine.dll", "tools/rc/processors/net9.0/BuildHost-netcore/cs/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/de/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/es/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/fr/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/it/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/ja/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/ko/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/pl/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/pt-BR/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/ru/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/tr/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/zh-Hans/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/BuildHost-netcore/zh-Hant/System.CommandLine.resources.dll", "tools/rc/processors/net9.0/CommonServiceLocator.dll", "tools/rc/processors/net9.0/Humanizer.dll", "tools/rc/processors/net9.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.CSharp.Features.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.CSharp.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.Elfie.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.ExternalAccess.RazorCompiler.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.Features.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.Scripting.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.VisualBasic.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.Workspaces.dll", "tools/rc/processors/net9.0/Microsoft.CodeAnalysis.dll", "tools/rc/processors/net9.0/Microsoft.DiaSymReader.dll", "tools/rc/processors/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/rc/processors/net9.0/Microsoft.Extensions.DependencyInjection.dll", "tools/rc/processors/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "tools/rc/processors/net9.0/Microsoft.Extensions.Logging.dll", "tools/rc/processors/net9.0/Microsoft.Extensions.Options.dll", "tools/rc/processors/net9.0/Microsoft.Extensions.Primitives.dll", "tools/rc/processors/net9.0/Microsoft.NET.StringTools.dll", "tools/rc/processors/net9.0/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "tools/rc/processors/net9.0/Newtonsoft.Json.dll", "tools/rc/processors/net9.0/System.CodeDom.dll", "tools/rc/processors/net9.0/System.Composition.AttributedModel.dll", "tools/rc/processors/net9.0/System.Composition.Convention.dll", "tools/rc/processors/net9.0/System.Composition.Hosting.dll", "tools/rc/processors/net9.0/System.Composition.Runtime.dll", "tools/rc/processors/net9.0/System.Composition.TypedParts.dll", "tools/rc/processors/net9.0/System.Configuration.ConfigurationManager.dll", "tools/rc/processors/net9.0/System.Diagnostics.EventLog.dll", "tools/rc/processors/net9.0/System.Reactive.dll", "tools/rc/processors/net9.0/System.Reflection.MetadataLoadContext.dll", "tools/rc/processors/net9.0/System.Resources.Extensions.dll", "tools/rc/processors/net9.0/System.Security.Cryptography.Pkcs.dll", "tools/rc/processors/net9.0/System.Security.Cryptography.ProtectedData.dll", "tools/rc/processors/net9.0/System.Security.Cryptography.Xml.dll", "tools/rc/processors/net9.0/System.Security.Permissions.dll", "tools/rc/processors/net9.0/System.Windows.Extensions.dll", "tools/rc/processors/net9.0/Uno.Core.Extensions.Disposables.dll", "tools/rc/processors/net9.0/Uno.Core.Extensions.Logging.Singleton.dll", "tools/rc/processors/net9.0/Uno.Core.Extensions.dll", "tools/rc/processors/net9.0/Uno.UI.RemoteControl.Messaging.dll", "tools/rc/processors/net9.0/Uno.UI.RemoteControl.Messaging.pdb", "tools/rc/processors/net9.0/Uno.UI.RemoteControl.Server.Processors.dll", "tools/rc/processors/net9.0/Uno.UI.RemoteControl.Server.Processors.pdb", "tools/rc/processors/net9.0/Uno.UI.RemoteControl.Server.dll", "tools/rc/processors/net9.0/Uno.UI.RemoteControl.Server.pdb", "uno-runtime/net8.0/skia/Uno.UI.RemoteControl.dll", "uno-runtime/net8.0/skia/Uno.UI.RemoteControl.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.RemoteControl.dll", "uno-runtime/net8.0/webassembly/Uno.UI.RemoteControl.pdb", "uno-runtime/net9.0/skia/Uno.UI.RemoteControl.dll", "uno-runtime/net9.0/skia/Uno.UI.RemoteControl.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.RemoteControl.dll", "uno-runtime/net9.0/webassembly/Uno.UI.RemoteControl.pdb", "uno.png", "uno.winui.devserver.6.0.797.nupkg.sha512", "uno.winui.devserver.nuspec"]}, "Uno.WinUI.DevServer.Messaging/6.0.797": {"sha512": "fGnVT/yfwe7+61E3ct4G+zI8yjPPsBgKmrcYHNY1P6luBgvw/FIMGH+AQ1zgAXSO5ZFvTBsTK8YeurUoVnZVug==", "type": "package", "path": "uno.winui.devserver.messaging/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Uno.UI.RemoteControl.Messaging.dll", "lib/netstandard2.0/Uno.UI.RemoteControl.Messaging.pdb", "uno.png", "uno.winui.devserver.messaging.6.0.797.nupkg.sha512", "uno.winui.devserver.messaging.nuspec"]}, "Uno.WinUI.Graphics2DSK/6.0.797": {"sha512": "hYZW31HfD/3exidgcuLTKshd1jqFpT3UnIost9Wb5gyLvCXUWqZLkoTvmew+JwHyZUMtNwWl8Kp4xFJ/tNeFwA==", "type": "package", "path": "uno.winui.graphics2dsk/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Uno.WinUI.Graphics2DSK.dll", "lib/net8.0/Uno.WinUI.Graphics2DSK.pdb", "lib/net9.0/Uno.WinUI.Graphics2DSK.dll", "lib/net9.0/Uno.WinUI.Graphics2DSK.pdb", "uno.png", "uno.winui.graphics2dsk.6.0.797.nupkg.sha512", "uno.winui.graphics2dsk.nuspec"]}, "Uno.WinUI.Lottie/6.0.797": {"sha512": "DZHXjQDh9UvsyCs1XzeB6unGx/IIOoYioCl7awAjexZAXRw/Uqxn1jhircSdwyNSOequBYCa1evOXc/zqOugdA==", "type": "package", "path": "uno.winui.lottie/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/_._", "build/uap10.0.16299/_._", "build/uap10.0.19041/_._", "buildTransitive/Uno.WinUI.Lottie.targets", "lib/net8.0-android/Uno.UI.Lottie.dll", "lib/net8.0-ios17.0/Uno.UI.Lottie.dll", "lib/net8.0-maccatalyst17.0/Uno.UI.Lottie.dll", "lib/net8.0-tvos17.0/Uno.UI.Lottie.dll", "lib/net8.0/Uno.UI.Lottie.dll", "lib/net9.0-android/Uno.UI.Lottie.dll", "lib/net9.0-ios18.0/Uno.UI.Lottie.dll", "lib/net9.0-maccatalyst18.0/Uno.UI.Lottie.dll", "lib/net9.0-tvos18.0/Uno.UI.Lottie.dll", "lib/net9.0/Uno.UI.Lottie.dll", "uno-runtime/net8.0/skia/Uno.UI.Lottie.deps.json", "uno-runtime/net8.0/skia/Uno.UI.Lottie.dll", "uno-runtime/net8.0/skia/Uno.UI.Lottie.dll.config", "uno-runtime/net8.0/skia/Uno.UI.Lottie.pdb", "uno-runtime/net8.0/webassembly/Uno.UI.Lottie.deps.json", "uno-runtime/net8.0/webassembly/Uno.UI.Lottie.dll", "uno-runtime/net8.0/webassembly/Uno.UI.Lottie.dll.config", "uno-runtime/net8.0/webassembly/Uno.UI.Lottie.pdb", "uno-runtime/net9.0/skia/Uno.UI.Lottie.deps.json", "uno-runtime/net9.0/skia/Uno.UI.Lottie.dll", "uno-runtime/net9.0/skia/Uno.UI.Lottie.dll.config", "uno-runtime/net9.0/skia/Uno.UI.Lottie.pdb", "uno-runtime/net9.0/webassembly/Uno.UI.Lottie.deps.json", "uno-runtime/net9.0/webassembly/Uno.UI.Lottie.dll", "uno-runtime/net9.0/webassembly/Uno.UI.Lottie.dll.config", "uno-runtime/net9.0/webassembly/Uno.UI.Lottie.pdb", "uno.png", "uno.winui.lottie.6.0.797.nupkg.sha512", "uno.winui.lottie.nuspec"]}, "Uno.WinUI.Runtime.Skia/6.0.797": {"sha512": "j7TvBlshRRz0n5FNPJq88so7RrxJ5CQikceSlQ0ycXefQr6HeK6EFaAg+wt3E303rquT4MrCaaNOpbfi9JgRng==", "type": "package", "path": "uno.winui.runtime.skia/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Uno.UI.Runtime.Skia.dll", "lib/net8.0/Uno.UI.Runtime.Skia.pdb", "lib/net9.0/Uno.UI.Runtime.Skia.dll", "lib/net9.0/Uno.UI.Runtime.Skia.pdb", "uno.png", "uno.winui.runtime.skia.6.0.797.nupkg.sha512", "uno.winui.runtime.skia.nuspec"]}, "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer/6.0.797": {"sha512": "vYFVL71tMmwPlQjNUxhD3PVu8uqKz9/toO09VY5KsifJoawyTXT0tNg3hEq46k2+SVFFfEh5blIf0tT7oIj9hw==", "type": "package", "path": "uno.winui.runtime.skia.linux.framebuffer/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.WinUI.Runtime.Skia.Linux.FrameBuffer.props", "buildTransitive/Uno.WinUI.Runtime.Skia.Linux.FrameBuffer.targets", "lib/net8.0/Uno.UI.Runtime.Skia.Linux.FrameBuffer.dll", "lib/net8.0/Uno.UI.Runtime.Skia.Linux.FrameBuffer.pdb", "lib/net9.0/Uno.UI.Runtime.Skia.Linux.FrameBuffer.dll", "lib/net9.0/Uno.UI.Runtime.Skia.Linux.FrameBuffer.pdb", "uno.png", "uno.winui.runtime.skia.linux.framebuffer.6.0.797.nupkg.sha512", "uno.winui.runtime.skia.linux.framebuffer.nuspec"]}, "Uno.WinUI.Runtime.Skia.MacOS/6.0.797": {"sha512": "R7noFjkmtQS+TNwO7YcK30oA6ma3YlngNbVllq2vEJi46/NPFDr3GpdXR0EDCiNchfIgX8BJ5yfSR9DPmYqzrg==", "type": "package", "path": "uno.winui.runtime.skia.macos/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.WinUI.Runtime.Skia.MacOS.props", "buildTransitive/Uno.WinUI.Runtime.Skia.MacOS.targets", "lib/net8.0/Uno.UI.Runtime.Skia.MacOS.dll", "lib/net8.0/Uno.UI.Runtime.Skia.MacOS.pdb", "lib/net9.0/Uno.UI.Runtime.Skia.MacOS.dll", "lib/net9.0/Uno.UI.Runtime.Skia.MacOS.pdb", "runtimes/osx/native/libUnoNativeMac.dylib", "uno.png", "uno.winui.runtime.skia.macos.6.0.797.nupkg.sha512", "uno.winui.runtime.skia.macos.nuspec"]}, "Uno.WinUI.Runtime.Skia.Win32/6.0.797": {"sha512": "GRlIEcmTyTqMbX8TOlwcWSxXOyeVlBm6opsPEtwHigZMNobwAZvsDzh7PZgB+RjdLNj4vbmacAHp7SG7viLlVQ==", "type": "package", "path": "uno.winui.runtime.skia.win32/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.WinUI.Runtime.Skia.Win32.props", "buildTransitive/Uno.WinUI.Runtime.Skia.Win32.targets", "lib/net8.0/Uno.UI.Runtime.Skia.Win32.Support.dll", "lib/net8.0/Uno.UI.Runtime.Skia.Win32.Support.pdb", "lib/net8.0/Uno.UI.Runtime.Skia.Win32.dll", "lib/net8.0/Uno.UI.Runtime.Skia.Win32.pdb", "lib/net9.0/Uno.UI.Runtime.Skia.Win32.Support.dll", "lib/net9.0/Uno.UI.Runtime.Skia.Win32.Support.pdb", "lib/net9.0/Uno.UI.Runtime.Skia.Win32.dll", "lib/net9.0/Uno.UI.Runtime.Skia.Win32.pdb", "uno.png", "uno.winui.runtime.skia.win32.6.0.797.nupkg.sha512", "uno.winui.runtime.skia.win32.nuspec"]}, "Uno.WinUI.Runtime.Skia.Wpf/6.0.797": {"sha512": "dCrWolicDiVkG/nlM0tNlBKDlmbXY852jxXw3RzeHhk1znONONfKtvFtWsCEGodekscQagARcdt5XVT6EjUA2g==", "type": "package", "path": "uno.winui.runtime.skia.wpf/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.WinUI.Runtime.Skia.Wpf.props", "buildTransitive/Uno.WinUI.Runtime.Skia.Wpf.targets", "lib/net8.0/Uno.UI.Runtime.Skia.Wpf.dll", "lib/net8.0/Uno.UI.Runtime.Skia.Wpf.pdb", "lib/net9.0/Uno.UI.Runtime.Skia.Wpf.dll", "lib/net9.0/Uno.UI.Runtime.Skia.Wpf.pdb", "uno.png", "uno.winui.runtime.skia.wpf.6.0.797.nupkg.sha512", "uno.winui.runtime.skia.wpf.nuspec"]}, "Uno.WinUI.Runtime.Skia.X11/6.0.797": {"sha512": "uyUoenlCfKmIeID05BC+arM91tL4GVdkPFq4qN4QQpWikq0dap48vT/PeJzo4SeEmxNvsPsopu9rjaK+Dn7oQQ==", "type": "package", "path": "uno.winui.runtime.skia.x11/6.0.797", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/Uno.WinUI.Runtime.Skia.X11.props", "buildTransitive/Uno.WinUI.Runtime.Skia.X11.targets", "lib/net8.0/Uno.UI.Runtime.Skia.X11.dll", "lib/net8.0/Uno.UI.Runtime.Skia.X11.pdb", "lib/net9.0/Uno.UI.Runtime.Skia.X11.dll", "lib/net9.0/Uno.UI.Runtime.Skia.X11.pdb", "uno.png", "uno.winui.runtime.skia.x11.6.0.797.nupkg.sha512", "uno.winui.runtime.skia.x11.nuspec"]}}, "projectFileDependencyGroups": {"net9.0-desktop1.0": ["Microsoft.Extensions.Logging.Console >= 9.0.6", "SkiaSharp.S<PERSON><PERSON> >= 3.119.0-preview.1.2", "SkiaSharp.Views.Uno.WinUI >= 3.119.0-preview.1.2", "Uno.Dsp.Tasks >= 1.4.0", "Uno.Extensions.Configuration >= 6.0.12", "Uno.Extensions.Core.WinUI >= 6.0.12", "Uno.Extensions.Hosting.WinUI >= 6.0.12", "Uno.Extensions.Http.Kiota >= 6.0.12", "Uno.Extensions.Http.WinUI >= 6.0.12", "Uno.Extensions.Localization.WinUI >= 6.0.12", "Uno.Extensions.Logging.WinUI >= 6.0.12", "Uno.Extensions.Navigation.Toolkit.WinUI >= 6.0.12", "Uno.Extensions.Navigation.WinUI >= 6.0.12", "Uno.Extensions.Reactive.Messaging >= 6.0.12", "Uno.Extensions.Reactive.WinUI >= 6.0.12", "Uno.Extensions.Serialization.Http >= 6.0.12", "Uno.Extensions.Serialization.Refit >= 6.0.12", "Uno.Fonts.OpenSans >= 2.7.1", "Uno.Material.WinUI >= 5.5.4", "Uno.Resizetizer >= 1.8.1", "Uno.Sdk.Extras >= 5.6.3", "Uno.Settings.DevServer >= 1.3.12", "Uno.Toolkit.WinUI >= 7.0.7", "Uno.Toolkit.WinUI.Material >= 7.0.7", "Uno.UI.Adapter.Microsoft.Extensions.Logging >= 6.0.797", "Uno.UI.HotDesign >= 1.13.6", "Uno.WinUI >= 6.0.797", "Uno.WinUI.DevServer >= 6.0.797", "Uno.WinUI.Graphics2DSK >= 6.0.797", "Uno.Win<PERSON>.<PERSON> >= 6.0.797", "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer >= 6.0.797", "Uno.WinUI.Runtime.Skia.MacOS >= 6.0.797", "Uno.WinUI.Runtime.Skia.Win32 >= 6.0.797", "Uno.WinUI.Runtime.Skia.Wpf >= 6.0.797", "Uno.WinUI.Runtime.Skia.X11 >= 6.0.797"]}, "packageFolders": {"q:\\.tools\\.nuget\\packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\UnoSkiaApp.csproj", "projectName": "UnoSkiaApp", "projectPath": "Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\UnoSkiaApp.csproj", "packagesPath": "q:\\.tools\\.nuget\\packages", "outputPath": "Q:\\test\\unotest3\\UnoSkiaApp\\UnoSkiaApp\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-desktop"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-desktop1.0": {"targetAlias": "net9.0-desktop", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1507"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-desktop1.0": {"targetAlias": "net9.0-desktop", "dependencies": {"Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.6, )", "autoReferenced": true}, "SkiaSharp.Skottie": {"target": "Package", "version": "[3.119.0-preview.1.2, )", "autoReferenced": true}, "SkiaSharp.Views.Uno.WinUI": {"target": "Package", "version": "[3.119.0-preview.1.2, )", "autoReferenced": true}, "Uno.Dsp.Tasks": {"target": "Package", "version": "[1.4.0, )", "autoReferenced": true}, "Uno.Extensions.Configuration": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Core.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Hosting.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Http.Kiota": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Http.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Localization.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Logging.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Navigation.Toolkit.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Navigation.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Reactive.Messaging": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Reactive.WinUI": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Serialization.Http": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Extensions.Serialization.Refit": {"target": "Package", "version": "[6.0.12, )", "autoReferenced": true}, "Uno.Fonts.OpenSans": {"target": "Package", "version": "[2.7.1, )", "autoReferenced": true}, "Uno.Material.WinUI": {"target": "Package", "version": "[5.5.4, )", "autoReferenced": true}, "Uno.Resizetizer": {"suppressParent": "All", "target": "Package", "version": "[1.8.1, )", "autoReferenced": true}, "Uno.Sdk.Extras": {"suppressParent": "All", "target": "Package", "version": "[5.6.3, )", "autoReferenced": true}, "Uno.Settings.DevServer": {"suppressParent": "All", "target": "Package", "version": "[1.3.12, )", "autoReferenced": true}, "Uno.Toolkit.WinUI": {"target": "Package", "version": "[7.0.7, )", "autoReferenced": true}, "Uno.Toolkit.WinUI.Material": {"target": "Package", "version": "[7.0.7, )", "autoReferenced": true}, "Uno.UI.Adapter.Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.UI.HotDesign": {"target": "Package", "version": "[1.13.6, )", "autoReferenced": true}, "Uno.WinUI": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.DevServer": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Graphics2DSK": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Lottie": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.Linux.FrameBuffer": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.MacOS": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.Win32": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.Wpf": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}, "Uno.WinUI.Runtime.Skia.X11": {"target": "Package", "version": "[6.0.797, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}